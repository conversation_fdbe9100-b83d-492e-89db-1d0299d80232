import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosResponseWithHTTP2, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace firebaseappdistribution_v1 {
    export interface Options extends GlobalOptions {
        version: 'v1';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Firebase App Distribution API
     *
     *
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const firebaseappdistribution = google.firebaseappdistribution('v1');
     * ```
     */
    export class Firebaseappdistribution {
        context: APIRequestContext;
        media: Resource$Media;
        projects: Resource$Projects;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * Information to read/write to blobstore2.
     */
    export interface Schema$GdataBlobstore2Info {
        /**
         * The blob generation id.
         */
        blobGeneration?: string | null;
        /**
         * The blob id, e.g., /blobstore/prod/playground/scotty
         */
        blobId?: string | null;
        /**
         * Read handle passed from Bigstore -\> Scotty for a GCS download. This is a signed, serialized blobstore2.ReadHandle proto which must never be set outside of Bigstore, and is not applicable to non-GCS media downloads.
         */
        downloadReadHandle?: string | null;
        /**
         * The blob read token. Needed to read blobs that have not been replicated. Might not be available until the final call.
         */
        readToken?: string | null;
        /**
         * Metadata passed from Blobstore -\> Scotty for a new GCS upload. This is a signed, serialized blobstore2.BlobMetadataContainer proto which must never be consumed outside of Bigstore, and is not applicable to non-GCS media uploads.
         */
        uploadMetadataContainer?: string | null;
    }
    /**
     * A sequence of media data references representing composite data. Introduced to support Bigstore composite objects. For details, visit http://go/bigstore-composites.
     */
    export interface Schema$GdataCompositeMedia {
        /**
         * Blobstore v1 reference, set if reference_type is BLOBSTORE_REF This should be the byte representation of a blobstore.BlobRef. Since Blobstore is deprecating v1, use blobstore2_info instead. For now, any v2 blob will also be represented in this field as v1 BlobRef.
         */
        blobRef?: string | null;
        /**
         * Blobstore v2 info, set if reference_type is BLOBSTORE_REF and it refers to a v2 blob.
         */
        blobstore2Info?: Schema$GdataBlobstore2Info;
        /**
         * A binary data reference for a media download. Serves as a technology-agnostic binary reference in some Google infrastructure. This value is a serialized storage_cosmo.BinaryReference proto. Storing it as bytes is a hack to get around the fact that the cosmo proto (as well as others it includes) doesn't support JavaScript. This prevents us from including the actual type of this field.
         */
        cosmoBinaryReference?: string | null;
        /**
         * crc32.c hash for the payload.
         */
        crc32cHash?: number | null;
        /**
         * Media data, set if reference_type is INLINE
         */
        inline?: string | null;
        /**
         * Size of the data, in bytes
         */
        length?: string | null;
        /**
         * MD5 hash for the payload.
         */
        md5Hash?: string | null;
        /**
         * Reference to a TI Blob, set if reference_type is BIGSTORE_REF.
         */
        objectId?: Schema$GdataObjectId;
        /**
         * Path to the data, set if reference_type is PATH
         */
        path?: string | null;
        /**
         * Describes what the field reference contains.
         */
        referenceType?: string | null;
        /**
         * SHA-1 hash for the payload.
         */
        sha1Hash?: string | null;
    }
    /**
     * Detailed Content-Type information from Scotty. The Content-Type of the media will typically be filled in by the header or Scotty's best_guess, but this extended information provides the backend with more information so that it can make a better decision if needed. This is only used on media upload requests from Scotty.
     */
    export interface Schema$GdataContentTypeInfo {
        /**
         * Scotty's best guess of what the content type of the file is.
         */
        bestGuess?: string | null;
        /**
         * The content type of the file derived by looking at specific bytes (i.e. "magic bytes") of the actual file.
         */
        fromBytes?: string | null;
        /**
         * The content type of the file derived from the file extension of the original file name used by the client.
         */
        fromFileName?: string | null;
        /**
         * The content type of the file as specified in the request headers, multipart headers, or RUPIO start request.
         */
        fromHeader?: string | null;
        /**
         * The content type of the file derived from the file extension of the URL path. The URL path is assumed to represent a file name (which is typically only true for agents that are providing a REST API).
         */
        fromUrlPath?: string | null;
    }
    /**
     * Backend response for a Diff get checksums response. For details on the Scotty Diff protocol, visit http://go/scotty-diff-protocol.
     */
    export interface Schema$GdataDiffChecksumsResponse {
        /**
         * Exactly one of these fields must be populated. If checksums_location is filled, the server will return the corresponding contents to the user. If object_location is filled, the server will calculate the checksums based on the content there and return that to the user. For details on the format of the checksums, see http://go/scotty-diff-protocol.
         */
        checksumsLocation?: Schema$GdataCompositeMedia;
        /**
         * The chunk size of checksums. Must be a multiple of 256KB.
         */
        chunkSizeBytes?: string | null;
        /**
         * If set, calculate the checksums based on the contents and return them to the caller.
         */
        objectLocation?: Schema$GdataCompositeMedia;
        /**
         * The total size of the server object.
         */
        objectSizeBytes?: string | null;
        /**
         * The object version of the object the checksums are being returned for.
         */
        objectVersion?: string | null;
    }
    /**
     * Backend response for a Diff download response. For details on the Scotty Diff protocol, visit http://go/scotty-diff-protocol.
     */
    export interface Schema$GdataDiffDownloadResponse {
        /**
         * The original object location.
         */
        objectLocation?: Schema$GdataCompositeMedia;
    }
    /**
     * A Diff upload request. For details on the Scotty Diff protocol, visit http://go/scotty-diff-protocol.
     */
    export interface Schema$GdataDiffUploadRequest {
        /**
         * The location of the checksums for the new object. Agents must clone the object located here, as the upload server will delete the contents once a response is received. For details on the format of the checksums, see http://go/scotty-diff-protocol.
         */
        checksumsInfo?: Schema$GdataCompositeMedia;
        /**
         * The location of the new object. Agents must clone the object located here, as the upload server will delete the contents once a response is received.
         */
        objectInfo?: Schema$GdataCompositeMedia;
        /**
         * The object version of the object that is the base version the incoming diff script will be applied to. This field will always be filled in.
         */
        objectVersion?: string | null;
    }
    /**
     * Backend response for a Diff upload request. For details on the Scotty Diff protocol, visit http://go/scotty-diff-protocol.
     */
    export interface Schema$GdataDiffUploadResponse {
        /**
         * The object version of the object at the server. Must be included in the end notification response. The version in the end notification response must correspond to the new version of the object that is now stored at the server, after the upload.
         */
        objectVersion?: string | null;
        /**
         * The location of the original file for a diff upload request. Must be filled in if responding to an upload start notification.
         */
        originalObject?: Schema$GdataCompositeMedia;
    }
    /**
     * Backend response for a Diff get version response. For details on the Scotty Diff protocol, visit http://go/scotty-diff-protocol.
     */
    export interface Schema$GdataDiffVersionResponse {
        /**
         * The total size of the server object.
         */
        objectSizeBytes?: string | null;
        /**
         * The version of the object stored at the server.
         */
        objectVersion?: string | null;
    }
    /**
     * Parameters specific to media downloads.
     */
    export interface Schema$GdataDownloadParameters {
        /**
         * A boolean to be returned in the response to Scotty. Allows/disallows gzip encoding of the payload content when the server thinks it's advantageous (hence, does not guarantee compression) which allows Scotty to GZip the response to the client.
         */
        allowGzipCompression?: boolean | null;
        /**
         * Determining whether or not Apiary should skip the inclusion of any Content-Range header on its response to Scotty.
         */
        ignoreRange?: boolean | null;
    }
    /**
     * A reference to data stored on the filesystem, on GFS or in blobstore.
     */
    export interface Schema$GdataMedia {
        /**
         * Deprecated, use one of explicit hash type fields instead. Algorithm used for calculating the hash. As of 2011/01/21, "MD5" is the only possible value for this field. New values may be added at any time.
         */
        algorithm?: string | null;
        /**
         * Use object_id instead.
         */
        bigstoreObjectRef?: string | null;
        /**
         * Blobstore v1 reference, set if reference_type is BLOBSTORE_REF This should be the byte representation of a blobstore.BlobRef. Since Blobstore is deprecating v1, use blobstore2_info instead. For now, any v2 blob will also be represented in this field as v1 BlobRef.
         */
        blobRef?: string | null;
        /**
         * Blobstore v2 info, set if reference_type is BLOBSTORE_REF and it refers to a v2 blob.
         */
        blobstore2Info?: Schema$GdataBlobstore2Info;
        /**
         * A composite media composed of one or more media objects, set if reference_type is COMPOSITE_MEDIA. The media length field must be set to the sum of the lengths of all composite media objects. Note: All composite media must have length specified.
         */
        compositeMedia?: Schema$GdataCompositeMedia[];
        /**
         * MIME type of the data
         */
        contentType?: string | null;
        /**
         * Extended content type information provided for Scotty uploads.
         */
        contentTypeInfo?: Schema$GdataContentTypeInfo;
        /**
         * A binary data reference for a media download. Serves as a technology-agnostic binary reference in some Google infrastructure. This value is a serialized storage_cosmo.BinaryReference proto. Storing it as bytes is a hack to get around the fact that the cosmo proto (as well as others it includes) doesn't support JavaScript. This prevents us from including the actual type of this field.
         */
        cosmoBinaryReference?: string | null;
        /**
         * For Scotty Uploads: Scotty-provided hashes for uploads For Scotty Downloads: (WARNING: DO NOT USE WITHOUT PERMISSION FROM THE SCOTTY TEAM.) A Hash provided by the agent to be used to verify the data being downloaded. Currently only supported for inline payloads. Further, only crc32c_hash is currently supported.
         */
        crc32cHash?: number | null;
        /**
         * Set if reference_type is DIFF_CHECKSUMS_RESPONSE.
         */
        diffChecksumsResponse?: Schema$GdataDiffChecksumsResponse;
        /**
         * Set if reference_type is DIFF_DOWNLOAD_RESPONSE.
         */
        diffDownloadResponse?: Schema$GdataDiffDownloadResponse;
        /**
         * Set if reference_type is DIFF_UPLOAD_REQUEST.
         */
        diffUploadRequest?: Schema$GdataDiffUploadRequest;
        /**
         * Set if reference_type is DIFF_UPLOAD_RESPONSE.
         */
        diffUploadResponse?: Schema$GdataDiffUploadResponse;
        /**
         * Set if reference_type is DIFF_VERSION_RESPONSE.
         */
        diffVersionResponse?: Schema$GdataDiffVersionResponse;
        /**
         * Parameters for a media download.
         */
        downloadParameters?: Schema$GdataDownloadParameters;
        /**
         * Original file name
         */
        filename?: string | null;
        /**
         * Deprecated, use one of explicit hash type fields instead. These two hash related fields will only be populated on Scotty based media uploads and will contain the content of the hash group in the NotificationRequest: http://cs/#google3/blobstore2/api/scotty/service/proto/upload_listener.proto&q=class:Hash Hex encoded hash value of the uploaded media.
         */
        hash?: string | null;
        /**
         * For Scotty uploads only. If a user sends a hash code and the backend has requested that Scotty verify the upload against the client hash, Scotty will perform the check on behalf of the backend and will reject it if the hashes don't match. This is set to true if Scotty performed this verification.
         */
        hashVerified?: boolean | null;
        /**
         * Media data, set if reference_type is INLINE
         */
        inline?: string | null;
        /**
         * |is_potential_retry| is set false only when Scotty is certain that it has not sent the request before. When a client resumes an upload, this field must be set true in agent calls, because Scotty cannot be certain that it has never sent the request before due to potential failure in the session state persistence.
         */
        isPotentialRetry?: boolean | null;
        /**
         * Size of the data, in bytes
         */
        length?: string | null;
        /**
         * Scotty-provided MD5 hash for an upload.
         */
        md5Hash?: string | null;
        /**
         * Media id to forward to the operation GetMedia. Can be set if reference_type is GET_MEDIA.
         */
        mediaId?: string | null;
        /**
         * Reference to a TI Blob, set if reference_type is BIGSTORE_REF.
         */
        objectId?: Schema$GdataObjectId;
        /**
         * Path to the data, set if reference_type is PATH
         */
        path?: string | null;
        /**
         * Describes what the field reference contains.
         */
        referenceType?: string | null;
        /**
         * Scotty-provided SHA1 hash for an upload.
         */
        sha1Hash?: string | null;
        /**
         * Scotty-provided SHA256 hash for an upload.
         */
        sha256Hash?: string | null;
        /**
         * Time at which the media data was last updated, in milliseconds since UNIX epoch
         */
        timestamp?: string | null;
        /**
         * A unique fingerprint/version id for the media data
         */
        token?: string | null;
    }
    /**
     * This is a copy of the tech.blob.ObjectId proto, which could not be used directly here due to transitive closure issues with JavaScript support; see http://b/8801763.
     */
    export interface Schema$GdataObjectId {
        /**
         * The name of the bucket to which this object belongs.
         */
        bucketName?: string | null;
        /**
         * Generation of the object. Generations are monotonically increasing across writes, allowing them to be be compared to determine which generation is newer. If this is omitted in a request, then you are requesting the live object. See http://go/bigstore-versions
         */
        generation?: string | null;
        /**
         * The name of the object.
         */
        objectName?: string | null;
    }
    /**
     * Android App Bundle (AAB) information for a Firebase app.
     */
    export interface Schema$GoogleFirebaseAppdistroV1AabInfo {
        /**
         * App bundle integration state. Only valid for android apps.
         */
        integrationState?: string | null;
        /**
         * The name of the `AabInfo` resource. Format: `projects/{project_number\}/apps/{app\}/aabInfo`
         */
        name?: string | null;
        /**
         * App bundle test certificate generated for the app. Set after the first app bundle is uploaded for this app.
         */
        testCertificate?: Schema$GoogleFirebaseAppdistroV1TestCertificate;
    }
    /**
     * The Request message for batch adding testers
     */
    export interface Schema$GoogleFirebaseAppdistroV1BatchAddTestersRequest {
        /**
         * Required. The email addresses of the tester resources to create. A maximum of 999 and a minimum of 1 tester can be created in a batch.
         */
        emails?: string[] | null;
    }
    /**
     * The Response message for `BatchAddTesters`.
     */
    export interface Schema$GoogleFirebaseAppdistroV1BatchAddTestersResponse {
        /**
         * The testers which are created and/or already exist
         */
        testers?: Schema$GoogleFirebaseAppdistroV1Tester[];
    }
    /**
     * The request message for `BatchDeleteReleases`.
     */
    export interface Schema$GoogleFirebaseAppdistroV1BatchDeleteReleasesRequest {
        /**
         * Required. The names of the release resources to delete. Format: `projects/{project_number\}/apps/{app_id\}/releases/{release_id\}` A maximum of 100 releases can be deleted per request.
         */
        names?: string[] | null;
    }
    /**
     * The request message for `BatchJoinGroup`
     */
    export interface Schema$GoogleFirebaseAppdistroV1BatchJoinGroupRequest {
        /**
         * Indicates whether to create tester resources based on `emails` if they don't exist yet.
         */
        createMissingTesters?: boolean | null;
        /**
         * Required. The emails of the testers to be added to the group. A maximum of 999 and a minimum of 1 tester can be created in a batch.
         */
        emails?: string[] | null;
    }
    /**
     * Request message for `BatchLeaveGroup`
     */
    export interface Schema$GoogleFirebaseAppdistroV1BatchLeaveGroupRequest {
        /**
         * Required. The email addresses of the testers to be removed from the group. A maximum of 999 and a minimum of 1 testers can be removed in a batch.
         */
        emails?: string[] | null;
    }
    /**
     * The request message for `BatchRemoveTesters`.
     */
    export interface Schema$GoogleFirebaseAppdistroV1BatchRemoveTestersRequest {
        /**
         * Required. The email addresses of the tester resources to removed. A maximum of 999 and a minimum of 1 testers can be deleted in a batch.
         */
        emails?: string[] | null;
    }
    /**
     * The response message for `BatchRemoveTesters`
     */
    export interface Schema$GoogleFirebaseAppdistroV1BatchRemoveTestersResponse {
        /**
         * List of deleted tester emails
         */
        emails?: string[] | null;
    }
    /**
     * The request message for `DistributeRelease`.
     */
    export interface Schema$GoogleFirebaseAppdistroV1DistributeReleaseRequest {
        /**
         * A list of group aliases (IDs) to be given access to this release. A combined maximum of 999 `testerEmails` and `groupAliases` can be specified in a single request.
         */
        groupAliases?: string[] | null;
        /**
         * A list of tester email addresses to be given access to this release. A combined maximum of 999 `testerEmails` and `groupAliases` can be specified in a single request.
         */
        testerEmails?: string[] | null;
    }
    /**
     * The response message for `DistributeRelease`.
     */
    export interface Schema$GoogleFirebaseAppdistroV1DistributeReleaseResponse {
    }
    /**
     * A feedback report submitted by a tester for a release.
     */
    export interface Schema$GoogleFirebaseAppdistroV1FeedbackReport {
        /**
         * Output only. The time when the feedback report was created.
         */
        createTime?: string | null;
        /**
         * Output only. A link to the Firebase console displaying the feedback report.
         */
        firebaseConsoleUri?: string | null;
        /**
         * The name of the feedback report resource. Format: `projects/{project_number\}/apps/{app\}/releases/{release\}/feedbackReports/{feedback_report\}`
         */
        name?: string | null;
        /**
         * Output only. A signed link (which expires in one hour) that lets you directly download the screenshot.
         */
        screenshotUri?: string | null;
        /**
         * Output only. The resource name of the tester who submitted the feedback report.
         */
        tester?: string | null;
        /**
         * Output only. The text of the feedback report.
         */
        text?: string | null;
    }
    /**
     * A group which can contain testers. A group can be invited to test apps in a Firebase project.
     */
    export interface Schema$GoogleFirebaseAppdistroV1Group {
        /**
         * Required. The display name of the group.
         */
        displayName?: string | null;
        /**
         * Output only. The number of invite links for this group.
         */
        inviteLinkCount?: number | null;
        /**
         * The name of the group resource. Format: `projects/{project_number\}/groups/{group_alias\}`
         */
        name?: string | null;
        /**
         * Output only. The number of releases this group is permitted to access.
         */
        releaseCount?: number | null;
        /**
         * Output only. The number of testers who are members of this group.
         */
        testerCount?: number | null;
    }
    /**
     * The response message for `ListFeedbackReports`.
     */
    export interface Schema$GoogleFirebaseAppdistroV1ListFeedbackReportsResponse {
        /**
         * The feedback reports
         */
        feedbackReports?: Schema$GoogleFirebaseAppdistroV1FeedbackReport[];
        /**
         * A short-lived token, which can be sent as `pageToken` to retrieve the next page. If this field is omitted, there are no subsequent pages.
         */
        nextPageToken?: string | null;
    }
    /**
     * The response message for `ListGroups`.
     */
    export interface Schema$GoogleFirebaseAppdistroV1ListGroupsResponse {
        /**
         * The groups listed.
         */
        groups?: Schema$GoogleFirebaseAppdistroV1Group[];
        /**
         * A short-lived token, which can be sent as `pageToken` to retrieve the next page. If this field is omitted, there are no subsequent pages.
         */
        nextPageToken?: string | null;
    }
    /**
     * The response message for `ListReleases`.
     */
    export interface Schema$GoogleFirebaseAppdistroV1ListReleasesResponse {
        /**
         * A short-lived token, which can be sent as `pageToken` to retrieve the next page. If this field is omitted, there are no subsequent pages.
         */
        nextPageToken?: string | null;
        /**
         * The releases
         */
        releases?: Schema$GoogleFirebaseAppdistroV1Release[];
    }
    /**
     * The response message for `ListTesters`.
     */
    export interface Schema$GoogleFirebaseAppdistroV1ListTestersResponse {
        /**
         * A short-lived token, which can be sent as `pageToken` to retrieve the next page. If this field is omitted, there are no subsequent pages.
         */
        nextPageToken?: string | null;
        /**
         * The testers listed.
         */
        testers?: Schema$GoogleFirebaseAppdistroV1Tester[];
    }
    /**
     * A release of a Firebase app.
     */
    export interface Schema$GoogleFirebaseAppdistroV1Release {
        /**
         * Output only. A signed link (which expires in one hour) to directly download the app binary (IPA/APK/AAB) file.
         */
        binaryDownloadUri?: string | null;
        /**
         * Output only. Build version of the release. For an Android release, the build version is the `versionCode`. For an iOS release, the build version is the `CFBundleVersion`.
         */
        buildVersion?: string | null;
        /**
         * Output only. The time the release was created.
         */
        createTime?: string | null;
        /**
         * Output only. Display version of the release. For an Android release, the display version is the `versionName`. For an iOS release, the display version is the `CFBundleShortVersionString`.
         */
        displayVersion?: string | null;
        /**
         * Output only. A link to the Firebase console displaying a single release.
         */
        firebaseConsoleUri?: string | null;
        /**
         * The name of the release resource. Format: `projects/{project_number\}/apps/{app_id\}/releases/{release_id\}`
         */
        name?: string | null;
        /**
         * Notes of the release.
         */
        releaseNotes?: Schema$GoogleFirebaseAppdistroV1ReleaseNotes;
        /**
         * Output only. A link to the release in the tester web clip or Android app that lets testers (which were granted access to the app) view release notes and install the app onto their devices.
         */
        testingUri?: string | null;
    }
    /**
     * Notes that belong to a release.
     */
    export interface Schema$GoogleFirebaseAppdistroV1ReleaseNotes {
        /**
         * The text of the release notes.
         */
        text?: string | null;
    }
    /**
     * App bundle test certificate
     */
    export interface Schema$GoogleFirebaseAppdistroV1TestCertificate {
        /**
         * Hex string of MD5 hash of the test certificate used to resign the AAB
         */
        hashMd5?: string | null;
        /**
         * Hex string of SHA1 hash of the test certificate used to resign the AAB
         */
        hashSha1?: string | null;
        /**
         * Hex string of SHA256 hash of the test certificate used to resign the AAB
         */
        hashSha256?: string | null;
    }
    /**
     * A person that can be invited to test apps in a Firebase project.
     */
    export interface Schema$GoogleFirebaseAppdistroV1Tester {
        /**
         * The name of the tester associated with the Google account used to accept the tester invitation.
         */
        displayName?: string | null;
        /**
         * The resource names of the groups this tester belongs to.
         */
        groups?: string[] | null;
        /**
         * Output only. The time the tester was last active. This is the most recent time the tester installed one of the apps. If they've never installed one or if the release no longer exists, this is the time the tester was added to the project.
         */
        lastActivityTime?: string | null;
        /**
         * The name of the tester resource. Format: `projects/{project_number\}/testers/{email_address\}`
         */
        name?: string | null;
    }
    /**
     * Operation metadata for `UploadRelease`.
     */
    export interface Schema$GoogleFirebaseAppdistroV1UploadReleaseMetadata {
    }
    /**
     * Request message for `UploadRelease`.
     */
    export interface Schema$GoogleFirebaseAppdistroV1UploadReleaseRequest {
        /**
         * Binary to upload
         */
        blob?: Schema$GdataMedia;
    }
    /**
     * Response message for `UploadRelease`.
     */
    export interface Schema$GoogleFirebaseAppdistroV1UploadReleaseResponse {
        /**
         * Release associated with the uploaded binary.
         */
        release?: Schema$GoogleFirebaseAppdistroV1Release;
        /**
         * Result of upload release.
         */
        result?: string | null;
    }
    /**
     * The request message for Operations.CancelOperation.
     */
    export interface Schema$GoogleLongrunningCancelOperationRequest {
    }
    /**
     * The response message for Operations.ListOperations.
     */
    export interface Schema$GoogleLongrunningListOperationsResponse {
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
        /**
         * A list of operations that matches the specified filter in the request.
         */
        operations?: Schema$GoogleLongrunningOperation[];
    }
    /**
     * This resource represents a long-running operation that is the result of a network API call.
     */
    export interface Schema$GoogleLongrunningOperation {
        /**
         * If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.
         */
        done?: boolean | null;
        /**
         * The error result of the operation in case of failure or cancellation.
         */
        error?: Schema$GoogleRpcStatus;
        /**
         * Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id\}`.
         */
        name?: string | null;
        /**
         * The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
         */
        response?: {
            [key: string]: any;
        } | null;
    }
    /**
     * The request message for Operations.WaitOperation.
     */
    export interface Schema$GoogleLongrunningWaitOperationRequest {
        /**
         * The maximum duration to wait before timing out. If left blank, the wait will be at most the time permitted by the underlying HTTP/RPC protocol. If RPC context deadline is also specified, the shorter one will be used.
         */
        timeout?: string | null;
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$GoogleProtobufEmpty {
    }
    /**
     * The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).
     */
    export interface Schema$GoogleRpcStatus {
        /**
         * The status code, which should be an enum value of google.rpc.Code.
         */
        code?: number | null;
        /**
         * A list of messages that carry the error details. There is a common set of message types for APIs to use.
         */
        details?: Array<{
            [key: string]: any;
        }> | null;
        /**
         * A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.
         */
        message?: string | null;
    }
    export class Resource$Media {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Uploads a binary. Uploading a binary can result in a new release being created, an update to an existing release, or a no-op if a release with the same binary already exists.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        upload(params: Params$Resource$Media$Upload, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        upload(params?: Params$Resource$Media$Upload, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningOperation>>;
        upload(params: Params$Resource$Media$Upload, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        upload(params: Params$Resource$Media$Upload, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        upload(params: Params$Resource$Media$Upload, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        upload(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
    }
    export interface Params$Resource$Media$Upload extends StandardParameters {
        /**
         * The name of the app resource. Format: `projects/{project_number\}/apps/{app_id\}`
         */
        app?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleFirebaseAppdistroV1UploadReleaseRequest;
        /**
         * Media metadata
         */
        media?: {
            /**
             * Media mime-type
             */
            mimeType?: string;
            /**
             * Media body contents
             */
            body?: any;
        };
    }
    export class Resource$Projects {
        context: APIRequestContext;
        apps: Resource$Projects$Apps;
        groups: Resource$Projects$Groups;
        testers: Resource$Projects$Testers;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Apps {
        context: APIRequestContext;
        releases: Resource$Projects$Apps$Releases;
        constructor(context: APIRequestContext);
        /**
         * Gets Android App Bundle (AAB) information for a Firebase app.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getAabInfo(params: Params$Resource$Projects$Apps$Getaabinfo, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getAabInfo(params?: Params$Resource$Projects$Apps$Getaabinfo, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleFirebaseAppdistroV1AabInfo>>;
        getAabInfo(params: Params$Resource$Projects$Apps$Getaabinfo, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getAabInfo(params: Params$Resource$Projects$Apps$Getaabinfo, options: MethodOptions | BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1AabInfo>, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1AabInfo>): void;
        getAabInfo(params: Params$Resource$Projects$Apps$Getaabinfo, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1AabInfo>): void;
        getAabInfo(callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1AabInfo>): void;
    }
    export interface Params$Resource$Projects$Apps$Getaabinfo extends StandardParameters {
        /**
         * Required. The name of the `AabInfo` resource to retrieve. Format: `projects/{project_number\}/apps/{app_id\}/aabInfo`
         */
        name?: string;
    }
    export class Resource$Projects$Apps$Releases {
        context: APIRequestContext;
        feedbackReports: Resource$Projects$Apps$Releases$Feedbackreports;
        operations: Resource$Projects$Apps$Releases$Operations;
        constructor(context: APIRequestContext);
        /**
         * Deletes releases. A maximum of 100 releases can be deleted per request.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        batchDelete(params: Params$Resource$Projects$Apps$Releases$Batchdelete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        batchDelete(params?: Params$Resource$Projects$Apps$Releases$Batchdelete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleProtobufEmpty>>;
        batchDelete(params: Params$Resource$Projects$Apps$Releases$Batchdelete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        batchDelete(params: Params$Resource$Projects$Apps$Releases$Batchdelete, options: MethodOptions | BodyResponseCallback<Schema$GoogleProtobufEmpty>, callback: BodyResponseCallback<Schema$GoogleProtobufEmpty>): void;
        batchDelete(params: Params$Resource$Projects$Apps$Releases$Batchdelete, callback: BodyResponseCallback<Schema$GoogleProtobufEmpty>): void;
        batchDelete(callback: BodyResponseCallback<Schema$GoogleProtobufEmpty>): void;
        /**
         * Distributes a release to testers. This call does the following: 1. Creates testers for the specified emails, if none exist. 2. Adds the testers and groups to the release. 3. Sends new testers an invitation email. 4. Sends existing testers a new release email. The request will fail with a `INVALID_ARGUMENT` if it contains a group that doesn't exist.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        distribute(params: Params$Resource$Projects$Apps$Releases$Distribute, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        distribute(params?: Params$Resource$Projects$Apps$Releases$Distribute, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleFirebaseAppdistroV1DistributeReleaseResponse>>;
        distribute(params: Params$Resource$Projects$Apps$Releases$Distribute, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        distribute(params: Params$Resource$Projects$Apps$Releases$Distribute, options: MethodOptions | BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1DistributeReleaseResponse>, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1DistributeReleaseResponse>): void;
        distribute(params: Params$Resource$Projects$Apps$Releases$Distribute, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1DistributeReleaseResponse>): void;
        distribute(callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1DistributeReleaseResponse>): void;
        /**
         * Gets a release.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Apps$Releases$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Apps$Releases$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleFirebaseAppdistroV1Release>>;
        get(params: Params$Resource$Projects$Apps$Releases$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Apps$Releases$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1Release>, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1Release>): void;
        get(params: Params$Resource$Projects$Apps$Releases$Get, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1Release>): void;
        get(callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1Release>): void;
        /**
         * Lists releases. By default, sorts by `createTime` in descending order.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Apps$Releases$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Apps$Releases$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleFirebaseAppdistroV1ListReleasesResponse>>;
        list(params: Params$Resource$Projects$Apps$Releases$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Apps$Releases$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1ListReleasesResponse>, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1ListReleasesResponse>): void;
        list(params: Params$Resource$Projects$Apps$Releases$List, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1ListReleasesResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1ListReleasesResponse>): void;
        /**
         * Updates a release.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Apps$Releases$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Apps$Releases$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleFirebaseAppdistroV1Release>>;
        patch(params: Params$Resource$Projects$Apps$Releases$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Apps$Releases$Patch, options: MethodOptions | BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1Release>, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1Release>): void;
        patch(params: Params$Resource$Projects$Apps$Releases$Patch, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1Release>): void;
        patch(callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1Release>): void;
    }
    export interface Params$Resource$Projects$Apps$Releases$Batchdelete extends StandardParameters {
        /**
         * Required. The name of the app resource, which is the parent of the release resources. Format: `projects/{project_number\}/apps/{app_id\}`
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleFirebaseAppdistroV1BatchDeleteReleasesRequest;
    }
    export interface Params$Resource$Projects$Apps$Releases$Distribute extends StandardParameters {
        /**
         * Required. The name of the release resource to distribute. Format: `projects/{project_number\}/apps/{app_id\}/releases/{release_id\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleFirebaseAppdistroV1DistributeReleaseRequest;
    }
    export interface Params$Resource$Projects$Apps$Releases$Get extends StandardParameters {
        /**
         * Required. The name of the release resource to retrieve. Format: projects/{project_number\}/apps/{app_id\}/releases/{release_id\}
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Apps$Releases$List extends StandardParameters {
        /**
         * The expression to filter releases listed in the response. To learn more about filtering, refer to [Google's AIP-160 standard](http://aip.dev/160). Supported fields: - `releaseNotes.text` supports `=` (can contain a wildcard character (`*`) at the beginning or end of the string) - `createTime` supports `<`, `<=`, `\>` and `\>=`, and expects an RFC-3339 formatted string Examples: - `createTime <= "2021-09-08T00:00:00+04:00"` - `releaseNotes.text="fixes" AND createTime \>= "2021-09-08T00:00:00.0Z"` - `releaseNotes.text="*v1.0.0-rc*"`
         */
        filter?: string;
        /**
         * The fields used to order releases. Supported fields: - `createTime` To specify descending order for a field, append a "desc" suffix, for example, `createTime desc`. If this parameter is not set, releases are ordered by `createTime` in descending order.
         */
        orderBy?: string;
        /**
         * The maximum number of releases to return. The service may return fewer than this value. The valid range is [1-100]; If unspecified (0), at most 25 releases are returned. Values above 100 are coerced to 100.
         */
        pageSize?: number;
        /**
         * A page token, received from a previous `ListReleases` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListReleases` must match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The name of the app resource, which is the parent of the release resources. Format: `projects/{project_number\}/apps/{app_id\}`
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Apps$Releases$Patch extends StandardParameters {
        /**
         * The name of the release resource. Format: `projects/{project_number\}/apps/{app_id\}/releases/{release_id\}`
         */
        name?: string;
        /**
         * The list of fields to update.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleFirebaseAppdistroV1Release;
    }
    export class Resource$Projects$Apps$Releases$Feedbackreports {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Deletes a feedback report.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Apps$Releases$Feedbackreports$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Apps$Releases$Feedbackreports$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleProtobufEmpty>>;
        delete(params: Params$Resource$Projects$Apps$Releases$Feedbackreports$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Apps$Releases$Feedbackreports$Delete, options: MethodOptions | BodyResponseCallback<Schema$GoogleProtobufEmpty>, callback: BodyResponseCallback<Schema$GoogleProtobufEmpty>): void;
        delete(params: Params$Resource$Projects$Apps$Releases$Feedbackreports$Delete, callback: BodyResponseCallback<Schema$GoogleProtobufEmpty>): void;
        delete(callback: BodyResponseCallback<Schema$GoogleProtobufEmpty>): void;
        /**
         * Gets a feedback report.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Apps$Releases$Feedbackreports$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Apps$Releases$Feedbackreports$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleFirebaseAppdistroV1FeedbackReport>>;
        get(params: Params$Resource$Projects$Apps$Releases$Feedbackreports$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Apps$Releases$Feedbackreports$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1FeedbackReport>, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1FeedbackReport>): void;
        get(params: Params$Resource$Projects$Apps$Releases$Feedbackreports$Get, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1FeedbackReport>): void;
        get(callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1FeedbackReport>): void;
        /**
         * Lists feedback reports. By default, sorts by `createTime` in descending order.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Apps$Releases$Feedbackreports$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Apps$Releases$Feedbackreports$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleFirebaseAppdistroV1ListFeedbackReportsResponse>>;
        list(params: Params$Resource$Projects$Apps$Releases$Feedbackreports$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Apps$Releases$Feedbackreports$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1ListFeedbackReportsResponse>, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1ListFeedbackReportsResponse>): void;
        list(params: Params$Resource$Projects$Apps$Releases$Feedbackreports$List, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1ListFeedbackReportsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1ListFeedbackReportsResponse>): void;
    }
    export interface Params$Resource$Projects$Apps$Releases$Feedbackreports$Delete extends StandardParameters {
        /**
         * Required. The name of the feedback report to delete. Format: projects/{project_number\}/apps/{app\}/releases/{release\}/feedbackReports/{feedback_report\}
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Apps$Releases$Feedbackreports$Get extends StandardParameters {
        /**
         * Required. The name of the feedback report to retrieve. Format: projects/{project_number\}/apps/{app\}/releases/{release\}/feedbackReports/{feedback_report\}
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Apps$Releases$Feedbackreports$List extends StandardParameters {
        /**
         * The maximum number of feedback reports to return. The service may return fewer than this value. The valid range is [1-100]; If unspecified (0), at most 25 feedback reports are returned. Values above 100 are coerced to 100.
         */
        pageSize?: number;
        /**
         * A page token, received from a previous `ListFeedbackReports` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListFeedbackReports` must match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The name of the release resource, which is the parent of the feedback report resources. Format: `projects/{project_number\}/apps/{app\}/releases/{release\}`
         */
        parent?: string;
    }
    export class Resource$Projects$Apps$Releases$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cancel(params: Params$Resource$Projects$Apps$Releases$Operations$Cancel, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        cancel(params?: Params$Resource$Projects$Apps$Releases$Operations$Cancel, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleProtobufEmpty>>;
        cancel(params: Params$Resource$Projects$Apps$Releases$Operations$Cancel, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cancel(params: Params$Resource$Projects$Apps$Releases$Operations$Cancel, options: MethodOptions | BodyResponseCallback<Schema$GoogleProtobufEmpty>, callback: BodyResponseCallback<Schema$GoogleProtobufEmpty>): void;
        cancel(params: Params$Resource$Projects$Apps$Releases$Operations$Cancel, callback: BodyResponseCallback<Schema$GoogleProtobufEmpty>): void;
        cancel(callback: BodyResponseCallback<Schema$GoogleProtobufEmpty>): void;
        /**
         * Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Apps$Releases$Operations$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Apps$Releases$Operations$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleProtobufEmpty>>;
        delete(params: Params$Resource$Projects$Apps$Releases$Operations$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Apps$Releases$Operations$Delete, options: MethodOptions | BodyResponseCallback<Schema$GoogleProtobufEmpty>, callback: BodyResponseCallback<Schema$GoogleProtobufEmpty>): void;
        delete(params: Params$Resource$Projects$Apps$Releases$Operations$Delete, callback: BodyResponseCallback<Schema$GoogleProtobufEmpty>): void;
        delete(callback: BodyResponseCallback<Schema$GoogleProtobufEmpty>): void;
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Apps$Releases$Operations$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Apps$Releases$Operations$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningOperation>>;
        get(params: Params$Resource$Projects$Apps$Releases$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Apps$Releases$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        get(params: Params$Resource$Projects$Apps$Releases$Operations$Get, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        get(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Apps$Releases$Operations$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Apps$Releases$Operations$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningListOperationsResponse>>;
        list(params: Params$Resource$Projects$Apps$Releases$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Apps$Releases$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>, callback: BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>): void;
        list(params: Params$Resource$Projects$Apps$Releases$Operations$List, callback: BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleLongrunningListOperationsResponse>): void;
        /**
         * Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        wait(params: Params$Resource$Projects$Apps$Releases$Operations$Wait, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        wait(params?: Params$Resource$Projects$Apps$Releases$Operations$Wait, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleLongrunningOperation>>;
        wait(params: Params$Resource$Projects$Apps$Releases$Operations$Wait, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        wait(params: Params$Resource$Projects$Apps$Releases$Operations$Wait, options: MethodOptions | BodyResponseCallback<Schema$GoogleLongrunningOperation>, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        wait(params: Params$Resource$Projects$Apps$Releases$Operations$Wait, callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
        wait(callback: BodyResponseCallback<Schema$GoogleLongrunningOperation>): void;
    }
    export interface Params$Resource$Projects$Apps$Releases$Operations$Cancel extends StandardParameters {
        /**
         * The name of the operation resource to be cancelled.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleLongrunningCancelOperationRequest;
    }
    export interface Params$Resource$Projects$Apps$Releases$Operations$Delete extends StandardParameters {
        /**
         * The name of the operation resource to be deleted.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Apps$Releases$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Apps$Releases$Operations$List extends StandardParameters {
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * The name of the operation's parent resource.
         */
        name?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
    }
    export interface Params$Resource$Projects$Apps$Releases$Operations$Wait extends StandardParameters {
        /**
         * The name of the operation resource to wait on.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleLongrunningWaitOperationRequest;
    }
    export class Resource$Projects$Groups {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Batch adds members to a group. The testers will gain access to all releases that the groups have access to.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        batchJoin(params: Params$Resource$Projects$Groups$Batchjoin, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        batchJoin(params?: Params$Resource$Projects$Groups$Batchjoin, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleProtobufEmpty>>;
        batchJoin(params: Params$Resource$Projects$Groups$Batchjoin, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        batchJoin(params: Params$Resource$Projects$Groups$Batchjoin, options: MethodOptions | BodyResponseCallback<Schema$GoogleProtobufEmpty>, callback: BodyResponseCallback<Schema$GoogleProtobufEmpty>): void;
        batchJoin(params: Params$Resource$Projects$Groups$Batchjoin, callback: BodyResponseCallback<Schema$GoogleProtobufEmpty>): void;
        batchJoin(callback: BodyResponseCallback<Schema$GoogleProtobufEmpty>): void;
        /**
         * Batch removed members from a group. The testers will lose access to all releases that the groups have access to.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        batchLeave(params: Params$Resource$Projects$Groups$Batchleave, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        batchLeave(params?: Params$Resource$Projects$Groups$Batchleave, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleProtobufEmpty>>;
        batchLeave(params: Params$Resource$Projects$Groups$Batchleave, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        batchLeave(params: Params$Resource$Projects$Groups$Batchleave, options: MethodOptions | BodyResponseCallback<Schema$GoogleProtobufEmpty>, callback: BodyResponseCallback<Schema$GoogleProtobufEmpty>): void;
        batchLeave(params: Params$Resource$Projects$Groups$Batchleave, callback: BodyResponseCallback<Schema$GoogleProtobufEmpty>): void;
        batchLeave(callback: BodyResponseCallback<Schema$GoogleProtobufEmpty>): void;
        /**
         * Create a group.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Groups$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Groups$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleFirebaseAppdistroV1Group>>;
        create(params: Params$Resource$Projects$Groups$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Groups$Create, options: MethodOptions | BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1Group>, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1Group>): void;
        create(params: Params$Resource$Projects$Groups$Create, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1Group>): void;
        create(callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1Group>): void;
        /**
         * Delete a group.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Groups$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Groups$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleProtobufEmpty>>;
        delete(params: Params$Resource$Projects$Groups$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Groups$Delete, options: MethodOptions | BodyResponseCallback<Schema$GoogleProtobufEmpty>, callback: BodyResponseCallback<Schema$GoogleProtobufEmpty>): void;
        delete(params: Params$Resource$Projects$Groups$Delete, callback: BodyResponseCallback<Schema$GoogleProtobufEmpty>): void;
        delete(callback: BodyResponseCallback<Schema$GoogleProtobufEmpty>): void;
        /**
         * Get a group.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Groups$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Groups$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleFirebaseAppdistroV1Group>>;
        get(params: Params$Resource$Projects$Groups$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Groups$Get, options: MethodOptions | BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1Group>, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1Group>): void;
        get(params: Params$Resource$Projects$Groups$Get, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1Group>): void;
        get(callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1Group>): void;
        /**
         * List groups.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Groups$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Groups$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleFirebaseAppdistroV1ListGroupsResponse>>;
        list(params: Params$Resource$Projects$Groups$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Groups$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1ListGroupsResponse>, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1ListGroupsResponse>): void;
        list(params: Params$Resource$Projects$Groups$List, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1ListGroupsResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1ListGroupsResponse>): void;
        /**
         * Update a group.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Groups$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Groups$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleFirebaseAppdistroV1Group>>;
        patch(params: Params$Resource$Projects$Groups$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Groups$Patch, options: MethodOptions | BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1Group>, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1Group>): void;
        patch(params: Params$Resource$Projects$Groups$Patch, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1Group>): void;
        patch(callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1Group>): void;
    }
    export interface Params$Resource$Projects$Groups$Batchjoin extends StandardParameters {
        /**
         * Required. The name of the group resource to which testers are added. Format: `projects/{project_number\}/groups/{group_alias\}`
         */
        group?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleFirebaseAppdistroV1BatchJoinGroupRequest;
    }
    export interface Params$Resource$Projects$Groups$Batchleave extends StandardParameters {
        /**
         * Required. The name of the group resource from which testers are removed. Format: `projects/{project_number\}/groups/{group_alias\}`
         */
        group?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleFirebaseAppdistroV1BatchLeaveGroupRequest;
    }
    export interface Params$Resource$Projects$Groups$Create extends StandardParameters {
        /**
         * Optional. The "alias" to use for the group, which will become the final component of the group's resource name. This value must be unique per project. The field is named `groupId` to comply with AIP guidance for user-specified IDs. This value should be 4-63 characters, and valid characters are `/a-z-/`. If not set, it will be generated based on the display name.
         */
        groupId?: string;
        /**
         * Required. The name of the project resource, which is the parent of the group resource. Format: `projects/{project_number\}`
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleFirebaseAppdistroV1Group;
    }
    export interface Params$Resource$Projects$Groups$Delete extends StandardParameters {
        /**
         * Required. The name of the group resource. Format: `projects/{project_number\}/groups/{group_alias\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Groups$Get extends StandardParameters {
        /**
         * Required. The name of the group resource to retrieve. Format: `projects/{project_number\}/groups/{group_alias\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Groups$List extends StandardParameters {
        /**
         * Optional. The maximum number of groups to return. The service may return fewer than this value. The valid range is [1-1000]; If unspecified (0), at most 25 groups are returned. Values above 1000 are coerced to 1000.
         */
        pageSize?: number;
        /**
         * Optional. A page token, received from a previous `ListGroups` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListGroups` must match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The name of the project resource, which is the parent of the group resources. Format: `projects/{project_number\}`
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Groups$Patch extends StandardParameters {
        /**
         * The name of the group resource. Format: `projects/{project_number\}/groups/{group_alias\}`
         */
        name?: string;
        /**
         * The list of fields to update.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleFirebaseAppdistroV1Group;
    }
    export class Resource$Projects$Testers {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Batch adds testers. This call adds testers for the specified emails if they don't already exist. Returns all testers specified in the request, including newly created and previously existing testers. This action is idempotent.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        batchAdd(params: Params$Resource$Projects$Testers$Batchadd, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        batchAdd(params?: Params$Resource$Projects$Testers$Batchadd, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleFirebaseAppdistroV1BatchAddTestersResponse>>;
        batchAdd(params: Params$Resource$Projects$Testers$Batchadd, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        batchAdd(params: Params$Resource$Projects$Testers$Batchadd, options: MethodOptions | BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1BatchAddTestersResponse>, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1BatchAddTestersResponse>): void;
        batchAdd(params: Params$Resource$Projects$Testers$Batchadd, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1BatchAddTestersResponse>): void;
        batchAdd(callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1BatchAddTestersResponse>): void;
        /**
         * Batch removes testers. If found, this call deletes testers for the specified emails. Returns all deleted testers.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        batchRemove(params: Params$Resource$Projects$Testers$Batchremove, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        batchRemove(params?: Params$Resource$Projects$Testers$Batchremove, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleFirebaseAppdistroV1BatchRemoveTestersResponse>>;
        batchRemove(params: Params$Resource$Projects$Testers$Batchremove, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        batchRemove(params: Params$Resource$Projects$Testers$Batchremove, options: MethodOptions | BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1BatchRemoveTestersResponse>, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1BatchRemoveTestersResponse>): void;
        batchRemove(params: Params$Resource$Projects$Testers$Batchremove, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1BatchRemoveTestersResponse>): void;
        batchRemove(callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1BatchRemoveTestersResponse>): void;
        /**
         * Lists testers and their resource ids.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Testers$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Testers$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleFirebaseAppdistroV1ListTestersResponse>>;
        list(params: Params$Resource$Projects$Testers$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Testers$List, options: MethodOptions | BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1ListTestersResponse>, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1ListTestersResponse>): void;
        list(params: Params$Resource$Projects$Testers$List, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1ListTestersResponse>): void;
        list(callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1ListTestersResponse>): void;
        /**
         * Update a tester. If the testers joins a group they gain access to all releases that the group has access to.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Testers$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Testers$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$GoogleFirebaseAppdistroV1Tester>>;
        patch(params: Params$Resource$Projects$Testers$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Testers$Patch, options: MethodOptions | BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1Tester>, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1Tester>): void;
        patch(params: Params$Resource$Projects$Testers$Patch, callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1Tester>): void;
        patch(callback: BodyResponseCallback<Schema$GoogleFirebaseAppdistroV1Tester>): void;
    }
    export interface Params$Resource$Projects$Testers$Batchadd extends StandardParameters {
        /**
         * Required. The name of the project resource. Format: `projects/{project_number\}`
         */
        project?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleFirebaseAppdistroV1BatchAddTestersRequest;
    }
    export interface Params$Resource$Projects$Testers$Batchremove extends StandardParameters {
        /**
         * Required. The name of the project resource. Format: `projects/{project_number\}`
         */
        project?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleFirebaseAppdistroV1BatchRemoveTestersRequest;
    }
    export interface Params$Resource$Projects$Testers$List extends StandardParameters {
        /**
         * Optional. The expression to filter testers listed in the response. To learn more about filtering, refer to [Google's AIP-160 standard](http://aip.dev/160). Supported fields: - `name` - `displayName` - `groups` Example: - `name = "projects/-/testers/<EMAIL>"` - `displayName = "Joe Sixpack"` - `groups = "projects/x/groups/qa-team"`
         */
        filter?: string;
        /**
         * Optional. The maximum number of testers to return. The service may return fewer than this value. The valid range is [1-1000]; If unspecified (0), at most 10 testers are returned. Values above 1000 are coerced to 1000.
         */
        pageSize?: number;
        /**
         * Optional. A page token, received from a previous `ListTesters` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListTesters` must match the call that provided the page token.
         */
        pageToken?: string;
        /**
         * Required. The name of the project resource, which is the parent of the tester resources. Format: `projects/{project_number\}`
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Testers$Patch extends StandardParameters {
        /**
         * The name of the tester resource. Format: `projects/{project_number\}/testers/{email_address\}`
         */
        name?: string;
        /**
         * The list of fields to update.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$GoogleFirebaseAppdistroV1Tester;
    }
    export {};
}
