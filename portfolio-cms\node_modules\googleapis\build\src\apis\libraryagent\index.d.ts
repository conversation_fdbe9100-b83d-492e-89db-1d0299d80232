/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { libraryagent_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof libraryagent_v1.Libraryagent;
};
export declare function libraryagent(version: 'v1'): libraryagent_v1.Libraryagent;
export declare function libraryagent(options: libraryagent_v1.Options): libraryagent_v1.Libraryagent;
declare const auth: AuthPlus;
export { auth };
export { libraryagent_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
