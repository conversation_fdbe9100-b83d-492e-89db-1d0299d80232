/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { localservices_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof localservices_v1.Localservices;
};
export declare function localservices(version: 'v1'): localservices_v1.Localservices;
export declare function localservices(options: localservices_v1.Options): localservices_v1.Localservices;
declare const auth: AuthPlus;
export { auth };
export { localservices_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
