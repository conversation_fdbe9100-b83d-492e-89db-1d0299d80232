/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { meet_v2 } from './v2';
export declare const VERSIONS: {
    v2: typeof meet_v2.Meet;
};
export declare function meet(version: 'v2'): meet_v2.Meet;
export declare function meet(options: meet_v2.Options): meet_v2.Meet;
declare const auth: AuthPlus;
export { auth };
export { meet_v2 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
