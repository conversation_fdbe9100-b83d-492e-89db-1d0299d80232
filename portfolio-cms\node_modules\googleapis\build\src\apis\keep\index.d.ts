/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { keep_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof keep_v1.Keep;
};
export declare function keep(version: 'v1'): keep_v1.Keep;
export declare function keep(options: keep_v1.Options): keep_v1.Keep;
declare const auth: AuthPlus;
export { auth };
export { keep_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
