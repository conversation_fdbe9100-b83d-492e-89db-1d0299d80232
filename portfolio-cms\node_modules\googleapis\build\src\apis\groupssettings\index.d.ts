/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { groupssettings_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof groupssettings_v1.Groupssettings;
};
export declare function groupssettings(version: 'v1'): groupssettings_v1.Groupssettings;
export declare function groupssettings(options: groupssettings_v1.Options): groupssettings_v1.Groupssettings;
declare const auth: AuthPlus;
export { auth };
export { groupssettings_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
