{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Navigation = registerClientReference(\n    function() { throw new Error(\"Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/navigation.tsx <module evaluation>\",\n    \"Navigation\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,+DACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Navigation = registerClientReference(\n    function() { throw new Error(\"Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/navigation.tsx\",\n    \"Navigation\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2CACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Footer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Footer() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/footer.tsx <module evaluation>\",\n    \"Footer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2DACA", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Footer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Footer() from the server but Foot<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/footer.tsx\",\n    \"Footer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,uCACA", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/blog-post-header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BlogPostHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call BlogPostHeader() from the server but BlogPostHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/blog/blog-post-header.tsx <module evaluation>\",\n    \"BlogPostHeader\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,0EACA", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/blog-post-header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BlogPostHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call BlogPostHeader() from the server but BlogPostHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/blog/blog-post-header.tsx\",\n    \"BlogPostHeader\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,sDACA", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/blog-post-content.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BlogPostContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call BlogPostContent() from the server but BlogPostContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/blog/blog-post-content.tsx <module evaluation>\",\n    \"BlogPostContent\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,2EACA", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/blog-post-content.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BlogPostContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call BlogPostContent() from the server but BlogPostContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/blog/blog-post-content.tsx\",\n    \"BlogPostContent\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,uDACA", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/blog-post-sidebar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BlogPostSidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call BlogPostSidebar() from the server but BlogPostSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/blog/blog-post-sidebar.tsx <module evaluation>\",\n    \"BlogPostSidebar\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,2EACA", "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/blog-post-sidebar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BlogPostSidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call BlogPostSidebar() from the server but BlogPostSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/blog/blog-post-sidebar.tsx\",\n    \"BlogPostSidebar\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,uDACA", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/related-posts.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const RelatedPosts = registerClientReference(\n    function() { throw new Error(\"Attempted to call RelatedPosts() from the server but RelatedPosts is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/blog/related-posts.tsx <module evaluation>\",\n    \"RelatedPosts\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,uEACA", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/related-posts.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const RelatedPosts = registerClientReference(\n    function() { throw new Error(\"Attempted to call RelatedPosts() from the server but RelatedPosts is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/blog/related-posts.tsx\",\n    \"RelatedPosts\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,mDACA", "debugId": null}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/blog-post-navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BlogPostNavigation = registerClientReference(\n    function() { throw new Error(\"Attempted to call BlogPostNavigation() from the server but BlogPostNavigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/blog/blog-post-navigation.tsx <module evaluation>\",\n    \"BlogPostNavigation\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,8EACA", "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/blog-post-navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BlogPostNavigation = registerClientReference(\n    function() { throw new Error(\"Attempted to call BlogPostNavigation() from the server but BlogPostNavigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/blog/blog-post-navigation.tsx\",\n    \"BlogPostNavigation\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,0DACA", "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/app/blog/%5Bslug%5D/page.tsx"], "sourcesContent": ["import { <PERSON>ada<PERSON> } from \"next\";\nimport { notFound } from \"next/navigation\";\nimport { Navigation } from \"@/components/navigation\";\nimport { Footer } from \"@/components/footer\";\nimport { BlogPostHeader } from \"@/components/blog/blog-post-header\";\nimport { BlogPostContent } from \"@/components/blog/blog-post-content\";\nimport { BlogPostSidebar } from \"@/components/blog/blog-post-sidebar\";\nimport { RelatedPosts } from \"@/components/blog/related-posts\";\nimport { BlogPostNavigation } from \"@/components/blog/blog-post-navigation\";\n\n// This would typically come from a CMS or database\nconst getBlogPost = async (slug: string) => {\n  const blogPosts = [\n    {\n      id: 1,\n      title: \"Building Scalable React Applications with TypeScript\",\n      excerpt: \"Learn how to structure large React applications using TypeScript, advanced patterns, and best practices for maintainability and performance.\",\n      content: `\n# Building Scalable React Applications with TypeScript\n\nBuilding scalable React applications is one of the most important skills for modern frontend developers. In this comprehensive guide, we'll explore how to structure large React applications using TypeScript, advanced patterns, and best practices.\n\n## Why TypeScript for React?\n\nTypeScript brings several advantages to React development:\n\n- **Type Safety**: Catch errors at compile time rather than runtime\n- **Better IDE Support**: Enhanced autocomplete, refactoring, and navigation\n- **Self-Documenting Code**: Types serve as inline documentation\n- **Easier Refactoring**: Confident code changes with type checking\n\n## Project Structure\n\nA well-organized project structure is crucial for scalability:\n\n\\`\\`\\`\nsrc/\n├── components/\n│   ├── ui/           # Reusable UI components\n│   ├── forms/        # Form components\n│   └── layout/       # Layout components\n├── hooks/            # Custom React hooks\n├── services/         # API services\n├── types/            # TypeScript type definitions\n├── utils/            # Utility functions\n└── stores/           # State management\n\\`\\`\\`\n\n## Component Design Patterns\n\n### 1. Compound Components\n\nCompound components allow you to create flexible and reusable component APIs:\n\n\\`\\`\\`typescript\ninterface TabsProps {\n  children: React.ReactNode;\n  defaultValue?: string;\n}\n\nexport const Tabs = ({ children, defaultValue }: TabsProps) => {\n  const [activeTab, setActiveTab] = useState(defaultValue);\n  \n  return (\n    <TabsContext.Provider value={{ activeTab, setActiveTab }}>\n      {children}\n    </TabsContext.Provider>\n  );\n};\n\nTabs.List = TabsList;\nTabs.Trigger = TabsTrigger;\nTabs.Content = TabsContent;\n\\`\\`\\`\n\n### 2. Render Props Pattern\n\nThe render props pattern provides flexibility in component composition:\n\n\\`\\`\\`typescript\ninterface DataFetcherProps<T> {\n  url: string;\n  children: (data: T | null, loading: boolean, error: Error | null) => React.ReactNode;\n}\n\nexport const DataFetcher = <T,>({ url, children }: DataFetcherProps<T>) => {\n  const [data, setData] = useState<T | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<Error | null>(null);\n\n  // Fetch logic here...\n\n  return children(data, loading, error);\n};\n\\`\\`\\`\n\n## State Management\n\nFor large applications, consider these state management approaches:\n\n### 1. Context + useReducer\n\nFor complex local state:\n\n\\`\\`\\`typescript\ninterface AppState {\n  user: User | null;\n  theme: 'light' | 'dark';\n  notifications: Notification[];\n}\n\ntype AppAction = \n  | { type: 'SET_USER'; payload: User }\n  | { type: 'TOGGLE_THEME' }\n  | { type: 'ADD_NOTIFICATION'; payload: Notification };\n\nconst appReducer = (state: AppState, action: AppAction): AppState => {\n  switch (action.type) {\n    case 'SET_USER':\n      return { ...state, user: action.payload };\n    case 'TOGGLE_THEME':\n      return { ...state, theme: state.theme === 'light' ? 'dark' : 'light' };\n    default:\n      return state;\n  }\n};\n\\`\\`\\`\n\n### 2. Zustand for Global State\n\nZustand provides a simple and powerful state management solution:\n\n\\`\\`\\`typescript\ninterface UserStore {\n  user: User | null;\n  setUser: (user: User) => void;\n  logout: () => void;\n}\n\nexport const useUserStore = create<UserStore>((set) => ({\n  user: null,\n  setUser: (user) => set({ user }),\n  logout: () => set({ user: null }),\n}));\n\\`\\`\\`\n\n## Performance Optimization\n\n### 1. Code Splitting\n\nUse React.lazy for component-level code splitting:\n\n\\`\\`\\`typescript\nconst LazyComponent = React.lazy(() => import('./LazyComponent'));\n\nconst App = () => (\n  <Suspense fallback={<Loading />}>\n    <LazyComponent />\n  </Suspense>\n);\n\\`\\`\\`\n\n### 2. Memoization\n\nUse React.memo and useMemo strategically:\n\n\\`\\`\\`typescript\nconst ExpensiveComponent = React.memo(({ data }: { data: ComplexData }) => {\n  const processedData = useMemo(() => {\n    return expensiveCalculation(data);\n  }, [data]);\n\n  return <div>{processedData}</div>;\n});\n\\`\\`\\`\n\n## Testing Strategies\n\n### 1. Component Testing\n\nUse React Testing Library for component tests:\n\n\\`\\`\\`typescript\nimport { render, screen, fireEvent } from '@testing-library/react';\nimport { Button } from './Button';\n\ntest('calls onClick when clicked', () => {\n  const handleClick = jest.fn();\n  render(<Button onClick={handleClick}>Click me</Button>);\n  \n  fireEvent.click(screen.getByRole('button'));\n  expect(handleClick).toHaveBeenCalledTimes(1);\n});\n\\`\\`\\`\n\n### 2. Custom Hook Testing\n\nTest custom hooks with renderHook:\n\n\\`\\`\\`typescript\nimport { renderHook, act } from '@testing-library/react';\nimport { useCounter } from './useCounter';\n\ntest('increments counter', () => {\n  const { result } = renderHook(() => useCounter());\n  \n  act(() => {\n    result.current.increment();\n  });\n  \n  expect(result.current.count).toBe(1);\n});\n\\`\\`\\`\n\n## Conclusion\n\nBuilding scalable React applications with TypeScript requires careful planning, good architecture decisions, and adherence to best practices. By following the patterns and techniques outlined in this guide, you'll be well-equipped to build maintainable and performant React applications.\n\nRemember that scalability is not just about code organization—it's also about team collaboration, documentation, and continuous improvement of your development processes.\n      `,\n      slug: \"building-scalable-react-applications-typescript\",\n      category: \"React\",\n      tags: [\"React\", \"TypeScript\", \"Architecture\", \"Best Practices\"],\n      publishedAt: \"2024-01-15\",\n      updatedAt: \"2024-01-15\",\n      readTime: \"8 min read\",\n      views: 1250,\n      featured: true,\n      author: {\n        name: \"Ashish Kamat\",\n        avatar: \"/ashish-profile.svg\",\n        bio: \"Full Stack Developer & UI/UX Designer\"\n      }\n    },\n    // Add more blog posts here...\n  ];\n\n  return blogPosts.find(post => post.slug === slug);\n};\n\ninterface BlogPostPageProps {\n  params: {\n    slug: string;\n  };\n}\n\nexport async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {\n  const { slug } = await params;\n  const post = await getBlogPost(slug);\n\n  if (!post) {\n    return {\n      title: \"Post Not Found\",\n    };\n  }\n\n  return {\n    title: `${post.title} - Ashish Kamat`,\n    description: post.excerpt,\n    openGraph: {\n      title: post.title,\n      description: post.excerpt,\n      type: \"article\",\n      publishedTime: post.publishedAt,\n      authors: [post.author.name],\n      tags: post.tags,\n    },\n    twitter: {\n      card: \"summary_large_image\",\n      title: post.title,\n      description: post.excerpt,\n    },\n  };\n}\n\nexport default async function BlogPostPage({ params }: BlogPostPageProps) {\n  const { slug } = await params;\n  const post = await getBlogPost(slug);\n\n  if (!post) {\n    notFound();\n  }\n\n  return (\n    <div className=\"min-h-screen\">\n      <Navigation />\n      <main className=\"pt-16\">\n        <article>\n          <BlogPostHeader post={post} />\n          <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n            <div className=\"grid lg:grid-cols-4 gap-12\">\n              <div className=\"lg:col-span-3\">\n                <BlogPostContent post={post} />\n                <BlogPostNavigation currentSlug={slug} />\n              </div>\n              <div className=\"lg:col-span-1\">\n                <BlogPostSidebar post={post} />\n              </div>\n            </div>\n          </div>\n        </article>\n        <RelatedPosts currentPost={post} />\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEA,mDAAmD;AACnD,MAAM,cAAc,OAAO;IACzB,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MA0MV,CAAC;YACD,MAAM;YACN,UAAU;YACV,MAAM;gBAAC;gBAAS;gBAAc;gBAAgB;aAAiB;YAC/D,aAAa;YACb,WAAW;YACX,UAAU;YACV,OAAO;YACP,UAAU;YACV,QAAQ;gBACN,MAAM;gBACN,QAAQ;gBACR,KAAK;YACP;QACF;KAED;IAED,OAAO,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;AAC9C;AAQO,eAAe,iBAAiB,EAAE,MAAM,EAAqB;IAClE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IACvB,MAAM,OAAO,MAAM,YAAY;IAE/B,IAAI,CAAC,MAAM;QACT,OAAO;YACL,OAAO;QACT;IACF;IAEA,OAAO;QACL,OAAO,GAAG,KAAK,KAAK,CAAC,eAAe,CAAC;QACrC,aAAa,KAAK,OAAO;QACzB,WAAW;YACT,OAAO,KAAK,KAAK;YACjB,aAAa,KAAK,OAAO;YACzB,MAAM;YACN,eAAe,KAAK,WAAW;YAC/B,SAAS;gBAAC,KAAK,MAAM,CAAC,IAAI;aAAC;YAC3B,MAAM,KAAK,IAAI;QACjB;QACA,SAAS;YACP,MAAM;YACN,OAAO,KAAK,KAAK;YACjB,aAAa,KAAK,OAAO;QAC3B;IACF;AACF;AAEe,eAAe,aAAa,EAAE,MAAM,EAAqB;IACtE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IACvB,MAAM,OAAO,MAAM,YAAY;IAE/B,IAAI,CAAC,MAAM;QACT,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gIAAA,CAAA,aAAU;;;;;0BACX,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;;0CACC,8OAAC,oJAAA,CAAA,iBAAc;gCAAC,MAAM;;;;;;0CACtB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,qJAAA,CAAA,kBAAe;oDAAC,MAAM;;;;;;8DACvB,8OAAC,wJAAA,CAAA,qBAAkB;oDAAC,aAAa;;;;;;;;;;;;sDAEnC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,qJAAA,CAAA,kBAAe;gDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAK/B,8OAAC,8IAAA,CAAA,eAAY;wBAAC,aAAa;;;;;;;;;;;;0BAE7B,8OAAC,4HAAA,CAAA,SAAM;;;;;;;;;;;AAGb", "debugId": null}}]}