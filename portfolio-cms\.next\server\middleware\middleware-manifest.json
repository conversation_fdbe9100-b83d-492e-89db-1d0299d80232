{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard/:path*{(\\\\.json)}?", "originalSource": "/dashboard/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/api/:path*{(\\\\.json)}?", "originalSource": "/api/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "GmR490q78K+bBIm3nmrLlRQO8rHHbnjcdhaBgWmTT64=", "__NEXT_PREVIEW_MODE_ID": "bc842248dfd9079db1e1dffe0498b3db", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "686f2494882a514f03ab14ad3adde1bf3aeadb56ac56996006b706790be293af", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "134b60734f73d91d46a217a3ebafa54dd688431b01267baf0d9efca6120ec2d4"}}}, "instrumentation": null, "functions": {}}