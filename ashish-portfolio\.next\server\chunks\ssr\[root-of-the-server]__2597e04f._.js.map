{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 350, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/command-palette.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { \n  Search, \n  Home, \n  User, \n  Briefcase, \n  Mail, \n  FileText,\n  ExternalLink,\n  Github,\n  Linkedin,\n  Twitter\n} from \"lucide-react\";\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogHeader,\n  DialogTitle,\n} from \"@/components/ui/dialog\";\nimport { Input } from \"@/components/ui/input\";\nimport { Badge } from \"@/components/ui/badge\";\n\ninterface Command {\n  id: string;\n  title: string;\n  description: string;\n  icon: React.ComponentType<any>;\n  action: () => void;\n  category: string;\n  keywords: string[];\n}\n\ninterface CommandPaletteProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n}\n\nexport function CommandPalette({ open, onOpenChange }: CommandPaletteProps) {\n  const [search, setSearch] = useState(\"\");\n  const [selectedIndex, setSelectedIndex] = useState(0);\n  const router = useRouter();\n\n  const commands: Command[] = [\n    // Navigation\n    {\n      id: \"home\",\n      title: \"Home\",\n      description: \"Go to homepage\",\n      icon: Home,\n      action: () => router.push(\"/\"),\n      category: \"Navigation\",\n      keywords: [\"home\", \"main\", \"landing\"],\n    },\n    {\n      id: \"about\",\n      title: \"About\",\n      description: \"Learn more about me\",\n      icon: User,\n      action: () => router.push(\"/about\"),\n      category: \"Navigation\",\n      keywords: [\"about\", \"bio\", \"experience\", \"skills\"],\n    },\n    {\n      id: \"projects\",\n      title: \"Projects\",\n      description: \"View my work and projects\",\n      icon: Briefcase,\n      action: () => router.push(\"/projects\"),\n      category: \"Navigation\",\n      keywords: [\"projects\", \"work\", \"portfolio\", \"showcase\"],\n    },\n    {\n      id: \"blog\",\n      title: \"Blog\",\n      description: \"Read my latest articles\",\n      icon: FileText,\n      action: () => router.push(\"/blog\"),\n      category: \"Navigation\",\n      keywords: [\"blog\", \"articles\", \"writing\", \"posts\"],\n    },\n    {\n      id: \"contact\",\n      title: \"Contact\",\n      description: \"Get in touch with me\",\n      icon: Mail,\n      action: () => router.push(\"/contact\"),\n      category: \"Navigation\",\n      keywords: [\"contact\", \"email\", \"message\", \"hire\"],\n    },\n    // External Links\n    {\n      id: \"github\",\n      title: \"GitHub\",\n      description: \"View my GitHub profile\",\n      icon: Github,\n      action: () => window.open(\"https://github.com/ashishkamat\", \"_blank\"),\n      category: \"Social\",\n      keywords: [\"github\", \"code\", \"repositories\", \"open source\"],\n    },\n    {\n      id: \"linkedin\",\n      title: \"LinkedIn\",\n      description: \"Connect with me on LinkedIn\",\n      icon: Linkedin,\n      action: () => window.open(\"https://linkedin.com/in/ashishkamat\", \"_blank\"),\n      category: \"Social\",\n      keywords: [\"linkedin\", \"professional\", \"network\", \"career\"],\n    },\n    {\n      id: \"twitter\",\n      title: \"Twitter\",\n      description: \"Follow me on Twitter\",\n      icon: Twitter,\n      action: () => window.open(\"https://twitter.com/ashishkamat\", \"_blank\"),\n      category: \"Social\",\n      keywords: [\"twitter\", \"social\", \"updates\", \"thoughts\"],\n    },\n    // Quick Actions\n    {\n      id: \"email\",\n      title: \"Send Email\",\n      description: \"Send me an email directly\",\n      icon: Mail,\n      action: () => window.open(\"mailto:<EMAIL>\", \"_blank\"),\n      category: \"Quick Actions\",\n      keywords: [\"email\", \"contact\", \"message\", \"hire\"],\n    },\n    {\n      id: \"resume\",\n      title: \"Download Resume\",\n      description: \"Download my latest resume\",\n      icon: ExternalLink,\n      action: () => window.open(\"/resume.pdf\", \"_blank\"),\n      category: \"Quick Actions\",\n      keywords: [\"resume\", \"cv\", \"download\", \"hire\"],\n    },\n  ];\n\n  const filteredCommands = commands.filter((command) => {\n    const searchLower = search.toLowerCase();\n    return (\n      command.title.toLowerCase().includes(searchLower) ||\n      command.description.toLowerCase().includes(searchLower) ||\n      command.keywords.some((keyword) => keyword.includes(searchLower))\n    );\n  });\n\n  const groupedCommands = filteredCommands.reduce((acc, command) => {\n    if (!acc[command.category]) {\n      acc[command.category] = [];\n    }\n    acc[command.category].push(command);\n    return acc;\n  }, {} as Record<string, Command[]>);\n\n  useEffect(() => {\n    setSelectedIndex(0);\n  }, [search]);\n\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if (!open) return;\n\n      if (e.key === \"ArrowDown\") {\n        e.preventDefault();\n        setSelectedIndex((prev) => \n          prev < filteredCommands.length - 1 ? prev + 1 : 0\n        );\n      } else if (e.key === \"ArrowUp\") {\n        e.preventDefault();\n        setSelectedIndex((prev) => \n          prev > 0 ? prev - 1 : filteredCommands.length - 1\n        );\n      } else if (e.key === \"Enter\") {\n        e.preventDefault();\n        if (filteredCommands[selectedIndex]) {\n          filteredCommands[selectedIndex].action();\n          onOpenChange(false);\n          setSearch(\"\");\n        }\n      }\n    };\n\n    window.addEventListener(\"keydown\", handleKeyDown);\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\n  }, [open, selectedIndex, filteredCommands, onOpenChange]);\n\n  const handleCommandSelect = (command: Command) => {\n    command.action();\n    onOpenChange(false);\n    setSearch(\"\");\n  };\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-2xl p-0 overflow-hidden\">\n        <DialogHeader className=\"p-4 pb-0\">\n          <DialogTitle className=\"sr-only\">Command Palette</DialogTitle>\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n            <Input\n              placeholder=\"Type a command or search...\"\n              value={search}\n              onChange={(e) => setSearch(e.target.value)}\n              className=\"pl-10 border-0 focus-visible:ring-0 text-base\"\n              autoFocus\n            />\n          </div>\n        </DialogHeader>\n\n        <div className=\"max-h-96 overflow-y-auto p-4 pt-0\">\n          {Object.keys(groupedCommands).length === 0 ? (\n            <div className=\"text-center py-8 text-muted-foreground\">\n              No commands found for \"{search}\"\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {Object.entries(groupedCommands).map(([category, commands]) => (\n                <div key={category}>\n                  <div className=\"text-xs font-medium text-muted-foreground uppercase tracking-wider mb-2 px-2\">\n                    {category}\n                  </div>\n                  <div className=\"space-y-1\">\n                    {commands.map((command, index) => {\n                      const globalIndex = filteredCommands.indexOf(command);\n                      const Icon = command.icon;\n                      \n                      return (\n                        <motion.button\n                          key={command.id}\n                          onClick={() => handleCommandSelect(command)}\n                          className={`w-full text-left p-3 rounded-lg transition-colors duration-200 flex items-center space-x-3 ${\n                            globalIndex === selectedIndex\n                              ? \"bg-accent text-accent-foreground\"\n                              : \"hover:bg-accent/50\"\n                          }`}\n                          whileHover={{ scale: 1.02 }}\n                          whileTap={{ scale: 0.98 }}\n                        >\n                          <div className={`p-2 rounded-md ${\n                            globalIndex === selectedIndex\n                              ? \"bg-primary text-primary-foreground\"\n                              : \"bg-muted\"\n                          }`}>\n                            <Icon className=\"h-4 w-4\" />\n                          </div>\n                          <div className=\"flex-1 min-w-0\">\n                            <div className=\"font-medium\">{command.title}</div>\n                            <div className=\"text-sm text-muted-foreground truncate\">\n                              {command.description}\n                            </div>\n                          </div>\n                          {command.category === \"Social\" && (\n                            <ExternalLink className=\"h-3 w-3 text-muted-foreground\" />\n                          )}\n                        </motion.button>\n                      );\n                    })}\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n\n        <div className=\"border-t p-3 text-xs text-muted-foreground flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"flex items-center space-x-1\">\n              <Badge variant=\"outline\" className=\"text-xs px-1.5 py-0.5\">↑↓</Badge>\n              <span>Navigate</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <Badge variant=\"outline\" className=\"text-xs px-1.5 py-0.5\">↵</Badge>\n              <span>Select</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <Badge variant=\"outline\" className=\"text-xs px-1.5 py-0.5\">Esc</Badge>\n              <span>Close</span>\n            </div>\n          </div>\n          <div className=\"text-muted-foreground/60\">\n            {filteredCommands.length} result{filteredCommands.length !== 1 ? 's' : ''}\n          </div>\n        </div>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAMA;AACA;AAxBA;;;;;;;;;AAyCO,SAAS,eAAe,EAAE,IAAI,EAAE,YAAY,EAAuB;IACxE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,WAAsB;QAC1B,aAAa;QACb;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,mMAAA,CAAA,OAAI;YACV,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAQ;gBAAQ;aAAU;QACvC;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,kMAAA,CAAA,OAAI;YACV,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAS;gBAAO;gBAAc;aAAS;QACpD;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,4MAAA,CAAA,YAAS;YACf,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAY;gBAAQ;gBAAa;aAAW;QACzD;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,8MAAA,CAAA,WAAQ;YACd,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAQ;gBAAY;gBAAW;aAAQ;QACpD;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,kMAAA,CAAA,OAAI;YACV,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAW;gBAAS;gBAAW;aAAO;QACnD;QACA,iBAAiB;QACjB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,sMAAA,CAAA,SAAM;YACZ,QAAQ,IAAM,OAAO,IAAI,CAAC,kCAAkC;YAC5D,UAAU;YACV,UAAU;gBAAC;gBAAU;gBAAQ;gBAAgB;aAAc;QAC7D;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,0MAAA,CAAA,WAAQ;YACd,QAAQ,IAAM,OAAO,IAAI,CAAC,uCAAuC;YACjE,UAAU;YACV,UAAU;gBAAC;gBAAY;gBAAgB;gBAAW;aAAS;QAC7D;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,wMAAA,CAAA,UAAO;YACb,QAAQ,IAAM,OAAO,IAAI,CAAC,mCAAmC;YAC7D,UAAU;YACV,UAAU;gBAAC;gBAAW;gBAAU;gBAAW;aAAW;QACxD;QACA,gBAAgB;QAChB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,kMAAA,CAAA,OAAI;YACV,QAAQ,IAAM,OAAO,IAAI,CAAC,gCAAgC;YAC1D,UAAU;YACV,UAAU;gBAAC;gBAAS;gBAAW;gBAAW;aAAO;QACnD;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,sNAAA,CAAA,eAAY;YAClB,QAAQ,IAAM,OAAO,IAAI,CAAC,eAAe;YACzC,UAAU;YACV,UAAU;gBAAC;gBAAU;gBAAM;gBAAY;aAAO;QAChD;KACD;IAED,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAC;QACxC,MAAM,cAAc,OAAO,WAAW;QACtC,OACE,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACrC,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAC3C,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAC,UAAY,QAAQ,QAAQ,CAAC;IAExD;IAEA,MAAM,kBAAkB,iBAAiB,MAAM,CAAC,CAAC,KAAK;QACpD,IAAI,CAAC,GAAG,CAAC,QAAQ,QAAQ,CAAC,EAAE;YAC1B,GAAG,CAAC,QAAQ,QAAQ,CAAC,GAAG,EAAE;QAC5B;QACA,GAAG,CAAC,QAAQ,QAAQ,CAAC,CAAC,IAAI,CAAC;QAC3B,OAAO;IACT,GAAG,CAAC;IAEJ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iBAAiB;IACnB,GAAG;QAAC;KAAO;IAEX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,IAAI,CAAC,MAAM;YAEX,IAAI,EAAE,GAAG,KAAK,aAAa;gBACzB,EAAE,cAAc;gBAChB,iBAAiB,CAAC,OAChB,OAAO,iBAAiB,MAAM,GAAG,IAAI,OAAO,IAAI;YAEpD,OAAO,IAAI,EAAE,GAAG,KAAK,WAAW;gBAC9B,EAAE,cAAc;gBAChB,iBAAiB,CAAC,OAChB,OAAO,IAAI,OAAO,IAAI,iBAAiB,MAAM,GAAG;YAEpD,OAAO,IAAI,EAAE,GAAG,KAAK,SAAS;gBAC5B,EAAE,cAAc;gBAChB,IAAI,gBAAgB,CAAC,cAAc,EAAE;oBACnC,gBAAgB,CAAC,cAAc,CAAC,MAAM;oBACtC,aAAa;oBACb,UAAU;gBACZ;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG;QAAC;QAAM;QAAe;QAAkB;KAAa;IAExD,MAAM,sBAAsB,CAAC;QAC3B,QAAQ,MAAM;QACd,aAAa;QACb,UAAU;IACZ;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;sCAAU;;;;;;sCACjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC,iIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;oCACzC,WAAU;oCACV,SAAS;;;;;;;;;;;;;;;;;;8BAKf,8OAAC;oBAAI,WAAU;8BACZ,OAAO,IAAI,CAAC,iBAAiB,MAAM,KAAK,kBACvC,8OAAC;wBAAI,WAAU;;4BAAyC;4BAC9B;4BAAO;;;;;;6CAGjC,8OAAC;wBAAI,WAAU;kCACZ,OAAO,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC,UAAU,SAAS,iBACxD,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDACZ;;;;;;kDAEH,8OAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,SAAS;4CACtB,MAAM,cAAc,iBAAiB,OAAO,CAAC;4CAC7C,MAAM,OAAO,QAAQ,IAAI;4CAEzB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gDAEZ,SAAS,IAAM,oBAAoB;gDACnC,WAAW,CAAC,2FAA2F,EACrG,gBAAgB,gBACZ,qCACA,sBACJ;gDACF,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;;kEAExB,8OAAC;wDAAI,WAAW,CAAC,eAAe,EAC9B,gBAAgB,gBACZ,uCACA,YACJ;kEACA,cAAA,8OAAC;4DAAK,WAAU;;;;;;;;;;;kEAElB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAe,QAAQ,KAAK;;;;;;0EAC3C,8OAAC;gEAAI,WAAU;0EACZ,QAAQ,WAAW;;;;;;;;;;;;oDAGvB,QAAQ,QAAQ,KAAK,0BACpB,8OAAC,sNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;+CAxBrB,QAAQ,EAAE;;;;;wCA4BrB;;;;;;;+BAvCM;;;;;;;;;;;;;;;8BA+ClB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAwB;;;;;;sDAC3D,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAwB;;;;;;sDAC3D,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAwB;;;;;;sDAC3D,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAGV,8OAAC;4BAAI,WAAU;;gCACZ,iBAAiB,MAAM;gCAAC;gCAAQ,iBAAiB,MAAM,KAAK,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;;;AAMnF", "debugId": null}}, {"offset": {"line": 861, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/navigation.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport Link from \"next/link\";\nimport { useTheme } from \"next-themes\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport {\n  Moon,\n  Sun,\n  Menu,\n  X,\n  Home,\n  User,\n  Briefcase,\n  Mail,\n  FileText,\n  Search\n} from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { CommandPalette } from \"@/components/command-palette\";\nimport { cn } from \"@/lib/utils\";\n\nconst navItems = [\n  { name: \"Home\", href: \"/\", icon: Home },\n  { name: \"About\", href: \"/about\", icon: User },\n  { name: \"Projects\", href: \"/projects\", icon: Briefcase },\n  { name: \"Blog\", href: \"/blog\", icon: FileText },\n  { name: \"Contact\", href: \"/contact\", icon: Mail },\n];\n\nexport function Navigation() {\n  const [isOpen, setIsOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n  const [commandPaletteOpen, setCommandPaletteOpen] = useState(false);\n  const { theme, setTheme } = useTheme();\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, []);\n\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if ((e.metaKey || e.ctrlKey) && e.key === \"k\") {\n        e.preventDefault();\n        setCommandPaletteOpen(true);\n      }\n    };\n\n    window.addEventListener(\"keydown\", handleKeyDown);\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\n  }, []);\n\n  const toggleTheme = () => {\n    setTheme(theme === \"dark\" ? \"light\" : \"dark\");\n  };\n\n  if (!mounted) {\n    return null;\n  }\n\n  return (\n    <motion.header\n      className={cn(\n        \"fixed top-0 left-0 right-0 z-50 transition-all duration-300\",\n        scrolled\n          ? \"bg-background/80 backdrop-blur-md border-b border-border\"\n          : \"bg-transparent\"\n      )}\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      <nav className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <motion.div\n            className=\"flex-shrink-0\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Link href=\"/\" className=\"text-2xl font-bold gradient-text-blue\">\n              AK\n            </Link>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:block\">\n            <div className=\"ml-10 flex items-baseline space-x-4\">\n              {navItems.map((item) => (\n                <motion.div\n                  key={item.name}\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <Link\n                    href={item.href}\n                    className=\"text-foreground/80 hover:text-foreground px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 hover:bg-accent\"\n                  >\n                    {item.name}\n                  </Link>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n\n          {/* Command Palette, Theme Toggle & Mobile Menu Button */}\n          <div className=\"flex items-center space-x-2\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setCommandPaletteOpen(true)}\n              className=\"hidden md:flex items-center space-x-2 text-muted-foreground hover:text-foreground\"\n            >\n              <Search className=\"h-4 w-4\" />\n              <span className=\"text-sm\">Search</span>\n              <kbd className=\"pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100\">\n                <span className=\"text-xs\">⌘</span>K\n              </kbd>\n            </Button>\n\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={toggleTheme}\n              className=\"w-9 h-9\"\n            >\n              {theme === \"dark\" ? (\n                <Sun className=\"h-4 w-4\" />\n              ) : (\n                <Moon className=\"h-4 w-4\" />\n              )}\n            </Button>\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={() => setIsOpen(!isOpen)}\n                className=\"w-9 h-9\"\n              >\n                {isOpen ? (\n                  <X className=\"h-4 w-4\" />\n                ) : (\n                  <Menu className=\"h-4 w-4\" />\n                )}\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <AnimatePresence>\n          {isOpen && (\n            <motion.div\n              className=\"md:hidden\"\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: \"auto\" }}\n              exit={{ opacity: 0, height: 0 }}\n              transition={{ duration: 0.3 }}\n            >\n              <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-background/95 backdrop-blur-md rounded-lg mt-2 border border-border\">\n                {navItems.map((item) => {\n                  const Icon = item.icon;\n                  return (\n                    <motion.div\n                      key={item.name}\n                      whileHover={{ scale: 1.02 }}\n                      whileTap={{ scale: 0.98 }}\n                    >\n                      <Link\n                        href={item.href}\n                        className=\"text-foreground/80 hover:text-foreground flex items-center px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 hover:bg-accent\"\n                        onClick={() => setIsOpen(false)}\n                      >\n                        <Icon className=\"h-4 w-4 mr-3\" />\n                        {item.name}\n                      </Link>\n                    </motion.div>\n                  );\n                })}\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </nav>\n\n      <CommandPalette\n        open={commandPaletteOpen}\n        onOpenChange={setCommandPaletteOpen}\n      />\n    </motion.header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;AApBA;;;;;;;;;;AAsBA,MAAM,WAAW;IACf;QAAE,MAAM;QAAQ,MAAM;QAAK,MAAM,mMAAA,CAAA,OAAI;IAAC;IACtC;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,kMAAA,CAAA,OAAI;IAAC;IAC5C;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,4MAAA,CAAA,YAAS;IAAC;IACvD;QAAE,MAAM;QAAQ,MAAM;QAAS,MAAM,8MAAA,CAAA,WAAQ;IAAC;IAC9C;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,kMAAA,CAAA,OAAI;IAAC;CACjD;AAEM,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,YAAY,OAAO,OAAO,GAAG;QAC/B;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,EAAE,GAAG,KAAK,KAAK;gBAC7C,EAAE,cAAc;gBAChB,sBAAsB;YACxB;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,SAAS,UAAU,SAAS,UAAU;IACxC;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA,WACI,6DACA;QAEN,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;QAAI;;0BAE5B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;0CAExB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAwC;;;;;;;;;;;0CAMnE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;sDAExB,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CARP,KAAK,IAAI;;;;;;;;;;;;;;;0CAgBtB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,sBAAsB;wCACrC,WAAU;;0DAEV,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAU;;;;;;oDAAQ;;;;;;;;;;;;;kDAItC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAET,UAAU,uBACT,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;iEAEf,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAKpB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,UAAU,CAAC;4CAC1B,WAAU;sDAET,uBACC,8OAAC,4LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;qEAEb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ1B,8OAAC,yLAAA,CAAA,kBAAe;kCACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BACjC,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAO;4BACtC,MAAM;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BAC9B,YAAY;gCAAE,UAAU;4BAAI;sCAE5B,cAAA,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC;oCACb,MAAM,OAAO,KAAK,IAAI;oCACtB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDAExB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,SAAS,IAAM,UAAU;;8DAEzB,8OAAC;oDAAK,WAAU;;;;;;gDACf,KAAK,IAAI;;;;;;;uCAVP,KAAK,IAAI;;;;;gCAcpB;;;;;;;;;;;;;;;;;;;;;;0BAOV,8OAAC,wIAAA,CAAA,iBAAc;gBACb,MAAM;gBACN,cAAc;;;;;;;;;;;;AAItB", "debugId": null}}, {"offset": {"line": 1236, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/footer.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { motion } from \"framer-motion\";\nimport { \n  Github, \n  Linkedin, \n  Twitter, \n  Mail, \n  Heart, \n  ArrowUp,\n  Code,\n  Coffee\n} from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\n\nconst footerLinks = {\n  navigation: [\n    { name: \"Home\", href: \"/\" },\n    { name: \"About\", href: \"/about\" },\n    { name: \"Projects\", href: \"/projects\" },\n    { name: \"Blog\", href: \"/blog\" },\n    { name: \"Contact\", href: \"/contact\" },\n  ],\n  services: [\n    { name: \"Full Stack Development\", href: \"/#services\" },\n    { name: \"UI/UX Design\", href: \"/#services\" },\n    { name: \"Mobile Development\", href: \"/#services\" },\n    { name: \"Consulting\", href: \"/#services\" },\n  ],\n  resources: [\n    { name: \"Blog\", href: \"/blog\" },\n    { name: \"Projects\", href: \"/projects\" },\n    { name: \"Tech Stack\", href: \"/#tech-stack\" },\n    { name: \"Contact\", href: \"/contact\" },\n  ],\n};\n\nconst socialLinks = [\n  {\n    name: \"GitHub\",\n    href: \"https://github.com/ashishkamat\",\n    icon: Github,\n    color: \"hover:text-gray-600 dark:hover:text-gray-300\",\n  },\n  {\n    name: \"LinkedIn\",\n    href: \"https://linkedin.com/in/ashishkamat\",\n    icon: Linkedin,\n    color: \"hover:text-blue-600\",\n  },\n  {\n    name: \"Twitter\",\n    href: \"https://twitter.com/ashishkamat\",\n    icon: Twitter,\n    color: \"hover:text-blue-500\",\n  },\n  {\n    name: \"Email\",\n    href: \"mailto:<EMAIL>\",\n    icon: Mail,\n    color: \"hover:text-green-600\",\n  },\n];\n\nexport function Footer() {\n  const scrollToTop = () => {\n    window.scrollTo({ top: 0, behavior: \"smooth\" });\n  };\n\n  return (\n    <footer className=\"bg-background border-t border-border\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Main Footer Content */}\n        <div className=\"py-12 lg:py-16\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\">\n            {/* Brand Section */}\n            <div className=\"lg:col-span-2\">\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.5 }}\n                viewport={{ once: true }}\n              >\n                <Link href=\"/\" className=\"text-2xl font-bold gradient-text-blue mb-4 inline-block\">\n                  Ashish Kamat\n                </Link>\n                <p className=\"text-muted-foreground mb-6 max-w-md\">\n                  Full Stack Developer & UI/UX Designer passionate about creating \n                  innovative digital experiences that make a difference.\n                </p>\n                \n                {/* Social Links */}\n                <div className=\"flex space-x-4\">\n                  {socialLinks.map((social, index) => {\n                    const Icon = social.icon;\n                    return (\n                      <motion.a\n                        key={social.name}\n                        href={social.href}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className={`p-2 rounded-lg bg-muted hover:bg-accent transition-all duration-300 ${social.color}`}\n                        whileHover={{ scale: 1.1 }}\n                        whileTap={{ scale: 0.9 }}\n                        initial={{ opacity: 0, scale: 0 }}\n                        whileInView={{ opacity: 1, scale: 1 }}\n                        transition={{ duration: 0.3, delay: index * 0.1 }}\n                        viewport={{ once: true }}\n                      >\n                        <Icon className=\"h-5 w-5\" />\n                      </motion.a>\n                    );\n                  })}\n                </div>\n              </motion.div>\n            </div>\n\n            {/* Navigation Links */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.1 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"font-semibold text-foreground mb-4\">Navigation</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.navigation.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-muted-foreground hover:text-foreground transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n\n            {/* Services Links */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.2 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"font-semibold text-foreground mb-4\">Services</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.services.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-muted-foreground hover:text-foreground transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n\n            {/* Resources Links */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.3 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"font-semibold text-foreground mb-4\">Resources</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.resources.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-muted-foreground hover:text-foreground transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n          </div>\n        </div>\n\n        {/* Bottom Footer */}\n        <motion.div\n          className=\"py-6 border-t border-border\"\n          initial={{ opacity: 0 }}\n          whileInView={{ opacity: 1 }}\n          transition={{ duration: 0.5, delay: 0.4 }}\n          viewport={{ once: true }}\n        >\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            {/* Copyright */}\n            <div className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n              <span>© 2024 Ashish Kamat. Made with</span>\n              <Heart className=\"h-4 w-4 text-red-500 animate-pulse\" />\n              <span>and</span>\n              <Coffee className=\"h-4 w-4 text-amber-600\" />\n              <span>in Kathmandu</span>\n            </div>\n\n            {/* Tech Stack & Back to Top */}\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n                <Code className=\"h-4 w-4\" />\n                <span>Built with Next.js & Tailwind CSS</span>\n              </div>\n              \n              <Button\n                variant=\"outline\"\n                size=\"icon\"\n                onClick={scrollToTop}\n                className=\"rounded-full hover:scale-110 transition-transform duration-200\"\n              >\n                <ArrowUp className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Fun Easter Egg */}\n        <motion.div\n          className=\"text-center py-4 border-t border-border/50\"\n          initial={{ opacity: 0 }}\n          whileInView={{ opacity: 1 }}\n          transition={{ duration: 0.5, delay: 0.6 }}\n          viewport={{ once: true }}\n        >\n          <p className=\"text-xs text-muted-foreground/70\">\n            🚀 This website is powered by{\" \"}\n            <span className=\"font-mono bg-muted px-1 py-0.5 rounded text-xs\">\n              {Math.floor(Math.random() * 100)}% coffee\n            </span>{\" \"}\n            and{\" \"}\n            <span className=\"font-mono bg-muted px-1 py-0.5 rounded text-xs\">\n              {Math.floor(Math.random() * 100)}% passion\n            </span>\n          </p>\n        </motion.div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAdA;;;;;;AAgBA,MAAM,cAAc;IAClB,YAAY;QACV;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IACD,UAAU;QACR;YAAE,MAAM;YAA0B,MAAM;QAAa;QACrD;YAAE,MAAM;YAAgB,MAAM;QAAa;QAC3C;YAAE,MAAM;YAAsB,MAAM;QAAa;QACjD;YAAE,MAAM;YAAc,MAAM;QAAa;KAC1C;IACD,WAAW;QACT;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAc,MAAM;QAAe;QAC3C;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;AACH;AAEA,MAAM,cAAc;IAClB;QACE,MAAM;QACN,MAAM;QACN,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,wMAAA,CAAA,UAAO;QACb,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;IACT;CACD;AAEM,SAAS;IACd,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YAAE,KAAK;YAAG,UAAU;QAAS;IAC/C;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;;sDAEvB,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDAA0D;;;;;;sDAGnF,8OAAC;4CAAE,WAAU;sDAAsC;;;;;;sDAMnD,8OAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,QAAQ;gDACxB,MAAM,OAAO,OAAO,IAAI;gDACxB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oDAEP,MAAM,OAAO,IAAI;oDACjB,QAAO;oDACP,KAAI;oDACJ,WAAW,CAAC,oEAAoE,EAAE,OAAO,KAAK,EAAE;oDAChG,YAAY;wDAAE,OAAO;oDAAI;oDACzB,UAAU;wDAAE,OAAO;oDAAI;oDACvB,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDAChC,aAAa;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDACpC,YAAY;wDAAE,UAAU;wDAAK,OAAO,QAAQ;oDAAI;oDAChD,UAAU;wDAAE,MAAM;oDAAK;8DAEvB,cAAA,8OAAC;wDAAK,WAAU;;;;;;mDAZX,OAAO,IAAI;;;;;4CAetB;;;;;;;;;;;;;;;;;0CAMN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAG,WAAU;kDACX,YAAY,UAAU,CAAC,GAAG,CAAC,CAAC,qBAC3B,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAaxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAG,WAAU;kDACX,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,qBACzB,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAaxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAG,WAAU;kDACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAe5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,aAAa;wBAAE,SAAS;oBAAE;oBAC1B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAK;;;;;;kDACN,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;kDAAK;;;;;;kDACN,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;kDAAK;;;;;;;;;;;;0CAIR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;kDAGR,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,4MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,aAAa;wBAAE,SAAS;oBAAE;oBAC1B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,8OAAC;wBAAE,WAAU;;4BAAmC;4BAChB;0CAC9B,8OAAC;gCAAK,WAAU;;oCACb,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;oCAAK;;;;;;;4BAC3B;4BAAI;4BACR;0CACJ,8OAAC;gCAAK,WAAU;;oCACb,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;oCAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}, {"offset": {"line": 1820, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1917, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/about/about-hero.tsx"], "sourcesContent": ["\"use client\";\n\nimport Image from \"next/image\";\nimport { motion } from \"framer-motion\";\nimport { Download, MapPin, Calendar, Coffee } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent } from \"@/components/ui/card\";\n\nexport function AboutHero() {\n  return (\n    <section className=\"py-20 bg-muted/30\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Content */}\n          <motion.div\n            className=\"space-y-8\"\n            initial={{ opacity: 0, x: -50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n          >\n            <div className=\"space-y-4\">\n              <motion.h1\n                className=\"text-4xl sm:text-5xl lg:text-6xl font-bold\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.2 }}\n              >\n                About <span className=\"gradient-text-blue\">Me</span>\n              </motion.h1>\n              \n              <motion.p\n                className=\"text-xl text-muted-foreground leading-relaxed\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.3 }}\n              >\n                I'm a passionate full-stack developer and UI/UX designer with over 3 years of experience \n                creating innovative digital solutions that make a real impact.\n              </motion.p>\n            </div>\n\n            <motion.div\n              className=\"prose prose-lg dark:prose-invert max-w-none\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.4 }}\n            >\n              <p>\n                My journey in tech started with a curiosity about how things work behind the scenes. \n                What began as tinkering with HTML and CSS has evolved into a deep passion for creating \n                seamless, user-centered digital experiences.\n              </p>\n              <p>\n                I specialize in modern web technologies like React, Next.js, and TypeScript, with a \n                strong focus on performance, accessibility, and user experience. I believe that great \n                software should not only function flawlessly but also delight users at every interaction.\n              </p>\n              <p>\n                When I'm not coding, you'll find me exploring new technologies, contributing to open-source \n                projects, or sharing knowledge with the developer community through blog posts and mentoring.\n              </p>\n            </motion.div>\n\n            <motion.div\n              className=\"flex flex-col sm:flex-row gap-4\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.5 }}\n            >\n              <Button size=\"lg\" className=\"group\">\n                <Download className=\"mr-2 h-4 w-4 group-hover:animate-bounce\" />\n                Download Resume\n              </Button>\n              <Button variant=\"outline\" size=\"lg\">\n                <Coffee className=\"mr-2 h-4 w-4\" />\n                Let's Chat\n              </Button>\n            </motion.div>\n          </motion.div>\n\n          {/* Image and Info Cards */}\n          <motion.div\n            className=\"relative\"\n            initial={{ opacity: 0, x: 50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n          >\n            <div className=\"relative w-80 h-80 mx-auto lg:w-96 lg:h-96 mb-8\">\n              <div className=\"absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl animate-pulse-glow\" />\n              <div className=\"absolute inset-2 bg-background rounded-2xl overflow-hidden\">\n                <Image\n                  src=\"/ashish-profile.svg\"\n                  alt=\"Ashish Kamat\"\n                  fill\n                  className=\"object-cover\"\n                  priority\n                />\n              </div>\n            </div>\n\n            {/* Info Cards */}\n            <div className=\"grid grid-cols-2 gap-4\">\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.6 }}\n              >\n                <Card className=\"text-center hover-lift\">\n                  <CardContent className=\"p-4\">\n                    <MapPin className=\"h-6 w-6 text-primary mx-auto mb-2\" />\n                    <div className=\"font-semibold\">Location</div>\n                    <div className=\"text-sm text-muted-foreground\">Kathmandu, Nepal</div>\n                  </CardContent>\n                </Card>\n              </motion.div>\n\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.7 }}\n              >\n                <Card className=\"text-center hover-lift\">\n                  <CardContent className=\"p-4\">\n                    <Calendar className=\"h-6 w-6 text-primary mx-auto mb-2\" />\n                    <div className=\"font-semibold\">Experience</div>\n                    <div className=\"text-sm text-muted-foreground\">3+ Years</div>\n                  </CardContent>\n                </Card>\n              </motion.div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAQO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;;0CAE5B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;4CAC1B;0DACO,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;;;;;;;kDAG7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wCACP,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;kDAC1B;;;;;;;;;;;;0CAMH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;gCAAI;;kDAEzB,8OAAC;kDAAE;;;;;;kDAKH,8OAAC;kDAAE;;;;;;kDAKH,8OAAC;kDAAE;;;;;;;;;;;;0CAML,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;gCAAI;;kDAEzB,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,WAAU;;0DAC1B,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAA4C;;;;;;;kDAGlE,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;;0DAC7B,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;kCAOzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;;0CAExC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,IAAI;4CACJ,WAAU;4CACV,QAAQ;;;;;;;;;;;;;;;;;0CAMd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;kDAEzB,cAAA,8OAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;wDAAI,WAAU;kEAAgB;;;;;;kEAC/B,8OAAC;wDAAI,WAAU;kEAAgC;;;;;;;;;;;;;;;;;;;;;;kDAKrD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;kDAEzB,cAAA,8OAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;wDAAI,WAAU;kEAAgB;;;;;;kEAC/B,8OAAC;wDAAI,WAAU;kEAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnE", "debugId": null}}, {"offset": {"line": 2317, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/about/experience.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { Calendar, MapPin, ExternalLink, Briefcase, Code, Users, TrendingUp } from \"lucide-react\";\nimport { VerticalTimeline, VerticalTimelineElement } from 'react-vertical-timeline-component';\nimport 'react-vertical-timeline-component/style.min.css';\nimport { Badge } from \"@/components/ui/badge\";\n\nconst experiences = [\n  {\n    title: \"Senior Full Stack Developer\",\n    company: \"TechCorp Solutions\",\n    location: \"Mumbai, India\",\n    period: \"2022 - Present\",\n    type: \"Full-time\",\n    description: \"Leading development of scalable web applications using React, Next.js, and Node.js. Mentoring junior developers and implementing best practices for code quality and performance.\",\n    achievements: [\n      \"Increased application performance by 40% through optimization\",\n      \"Led a team of 5 developers on multiple projects\",\n      \"Implemented CI/CD pipelines reducing deployment time by 60%\",\n      \"Architected microservices handling 1M+ requests daily\"\n    ],\n    technologies: [\"React\", \"Next.js\", \"TypeScript\", \"Node.js\", \"PostgreSQL\", \"AWS\"],\n    website: \"https://techcorp.com\",\n    icon: TrendingUp,\n    iconBg: \"#3b82f6\",\n    date: \"2022 - Present\"\n  },\n  {\n    title: \"Full Stack Developer\",\n    company: \"StartupXYZ\",\n    location: \"Remote\",\n    period: \"2021 - 2022\",\n    type: \"Full-time\",\n    description: \"Developed and maintained multiple client projects using modern web technologies. Collaborated with design teams to create pixel-perfect user interfaces.\",\n    achievements: [\n      \"Built 10+ responsive web applications from scratch\",\n      \"Reduced page load times by 50% through optimization\",\n      \"Implemented real-time features using WebSocket\",\n      \"Mentored 3 junior developers\"\n    ],\n    technologies: [\"React\", \"Vue.js\", \"Express.js\", \"MongoDB\", \"Firebase\"],\n    website: \"https://startupxyz.com\",\n    icon: Code,\n    iconBg: \"#10b981\",\n    date: \"2021 - 2022\"\n  },\n  {\n    title: \"Frontend Developer\",\n    company: \"Digital Agency Pro\",\n    location: \"Mumbai, India\",\n    period: \"2020 - 2021\",\n    type: \"Full-time\",\n    description: \"Focused on creating beautiful and functional user interfaces for various client projects. Worked closely with designers to bring mockups to life.\",\n    achievements: [\n      \"Delivered 20+ client projects on time and within budget\",\n      \"Improved client satisfaction scores by 25%\",\n      \"Implemented responsive designs for mobile-first approach\",\n      \"Created reusable component library\"\n    ],\n    technologies: [\"HTML\", \"CSS\", \"JavaScript\", \"React\", \"Sass\", \"Figma\"],\n    website: \"https://digitalagencypro.com\",\n    icon: Users,\n    iconBg: \"#8b5cf6\",\n    date: \"2020 - 2021\"\n  },\n  {\n    title: \"Junior Web Developer\",\n    company: \"WebDev Studio\",\n    location: \"Mumbai, India\",\n    period: \"2019 - 2020\",\n    type: \"Full-time\",\n    description: \"Started my professional journey learning modern web development practices and contributing to various client projects.\",\n    achievements: [\n      \"Completed 15+ small to medium-sized projects\",\n      \"Learned modern JavaScript frameworks and tools\",\n      \"Contributed to team's coding standards documentation\",\n      \"Achieved 95% client satisfaction rating\"\n    ],\n    technologies: [\"HTML\", \"CSS\", \"JavaScript\", \"jQuery\", \"Bootstrap\", \"PHP\"],\n    website: \"https://webdevstudio.com\",\n    icon: Briefcase,\n    iconBg: \"#f59e0b\",\n    date: \"2019 - 2020\"\n  }\n];\n\nexport function Experience() {\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  return (\n    <section ref={ref} className=\"py-20\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n        >\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n            <span className=\"gradient-text\">Work Experience</span>\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            My professional journey in web development, from junior developer to senior full-stack engineer.\n          </p>\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={inView ? { opacity: 1 } : {}}\n          transition={{ duration: 0.8, delay: 0.2 }}\n        >\n          <VerticalTimeline>\n            {experiences.map((experience, index) => {\n              const Icon = experience.icon;\n              return (\n                <VerticalTimelineElement\n                  key={index}\n                  className=\"vertical-timeline-element--work\"\n                  contentStyle={{\n                    background: 'hsl(var(--card))',\n                    color: 'hsl(var(--card-foreground))',\n                    border: '1px solid hsl(var(--border))',\n                    borderRadius: '12px',\n                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n                  }}\n                  contentArrowStyle={{\n                    borderRight: '7px solid hsl(var(--border))',\n                  }}\n                  date={experience.date}\n                  iconStyle={{\n                    background: experience.iconBg,\n                    color: '#fff',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                  }}\n                  icon={<Icon className=\"h-6 w-6\" />}\n                >\n                  <div className=\"space-y-4\">\n                    <div>\n                      <h3 className=\"text-xl font-bold\">{experience.title}</h3>\n                      <div className=\"flex items-center space-x-2 text-muted-foreground mt-1\">\n                        <span className=\"font-medium text-primary\">{experience.company}</span>\n                        {experience.website && (\n                          <a\n                            href={experience.website}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"hover:text-primary transition-colors\"\n                          >\n                            <ExternalLink className=\"h-4 w-4\" />\n                          </a>\n                        )}\n                      </div>\n                      <div className=\"flex flex-col sm:flex-row gap-2 text-sm text-muted-foreground mt-2\">\n                        <div className=\"flex items-center space-x-1\">\n                          <MapPin className=\"h-4 w-4\" />\n                          <span>{experience.location}</span>\n                        </div>\n                        <Badge variant=\"outline\" className=\"w-fit\">{experience.type}</Badge>\n                      </div>\n                    </div>\n\n                    <p className=\"text-muted-foreground\">{experience.description}</p>\n\n                    <div>\n                      <h4 className=\"font-semibold mb-2\">Key Achievements:</h4>\n                      <ul className=\"space-y-1\">\n                        {experience.achievements.map((achievement, achievementIndex) => (\n                          <li key={achievementIndex} className=\"flex items-start space-x-2 text-sm text-muted-foreground\">\n                            <div className=\"w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0\" />\n                            <span>{achievement}</span>\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n\n                    <div>\n                      <h4 className=\"font-semibold mb-2\">Technologies:</h4>\n                      <div className=\"flex flex-wrap gap-2\">\n                        {experience.technologies.map((tech) => (\n                          <Badge key={tech} variant=\"secondary\" className=\"text-xs\">\n                            {tech}\n                          </Badge>\n                        ))}\n                      </div>\n                    </div>\n                  </div>\n                </VerticalTimelineElement>\n              );\n            })}\n          </VerticalTimeline>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AAPA;;;;;;;;AASA,MAAM,cAAc;IAClB;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,QAAQ;QACR,MAAM;QACN,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAS;YAAW;YAAc;YAAW;YAAc;SAAM;QAChF,SAAS;QACT,MAAM,kNAAA,CAAA,aAAU;QAChB,QAAQ;QACR,MAAM;IACR;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,QAAQ;QACR,MAAM;QACN,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAS;YAAU;YAAc;YAAW;SAAW;QACtE,SAAS;QACT,MAAM,kMAAA,CAAA,OAAI;QACV,QAAQ;QACR,MAAM;IACR;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,QAAQ;QACR,MAAM;QACN,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAQ;YAAO;YAAc;YAAS;YAAQ;SAAQ;QACrE,SAAS;QACT,MAAM,oMAAA,CAAA,QAAK;QACX,QAAQ;QACR,MAAM;IACR;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,QAAQ;QACR,MAAM;QACN,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAQ;YAAO;YAAc;YAAU;YAAa;SAAM;QACzE,SAAS;QACT,MAAM,4MAAA,CAAA,YAAS;QACf,QAAQ;QACR,MAAM;IACR;CACD;AAEM,SAAS;IACd,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,qBACE,8OAAC;QAAQ,KAAK;QAAK,WAAU;kBAC3B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAKjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS,SAAS;wBAAE,SAAS;oBAAE,IAAI,CAAC;oBACpC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BAExC,cAAA,8OAAC,kLAAA,CAAA,mBAAgB;kCACd,YAAY,GAAG,CAAC,CAAC,YAAY;4BAC5B,MAAM,OAAO,WAAW,IAAI;4BAC5B,qBACE,8OAAC,kLAAA,CAAA,0BAAuB;gCAEtB,WAAU;gCACV,cAAc;oCACZ,YAAY;oCACZ,OAAO;oCACP,QAAQ;oCACR,cAAc;oCACd,WAAW;gCACb;gCACA,mBAAmB;oCACjB,aAAa;gCACf;gCACA,MAAM,WAAW,IAAI;gCACrB,WAAW;oCACT,YAAY,WAAW,MAAM;oCAC7B,OAAO;oCACP,SAAS;oCACT,YAAY;oCACZ,gBAAgB;gCAClB;gCACA,oBAAM,8OAAC;oCAAK,WAAU;;;;;;0CAEtB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAqB,WAAW,KAAK;;;;;;8DACnD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAA4B,WAAW,OAAO;;;;;;wDAC7D,WAAW,OAAO,kBACjB,8OAAC;4DACC,MAAM,WAAW,OAAO;4DACxB,QAAO;4DACP,KAAI;4DACJ,WAAU;sEAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;;;;;;;8DAI9B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,8OAAC;8EAAM,WAAW,QAAQ;;;;;;;;;;;;sEAE5B,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAAS,WAAW,IAAI;;;;;;;;;;;;;;;;;;sDAI/D,8OAAC;4CAAE,WAAU;sDAAyB,WAAW,WAAW;;;;;;sDAE5D,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAG,WAAU;8DACX,WAAW,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,iCACzC,8OAAC;4DAA0B,WAAU;;8EACnC,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;8EAAM;;;;;;;2DAFA;;;;;;;;;;;;;;;;sDAQf,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAI,WAAU;8DACZ,WAAW,YAAY,CAAC,GAAG,CAAC,CAAC,qBAC5B,8OAAC,iIAAA,CAAA,QAAK;4DAAY,SAAQ;4DAAY,WAAU;sEAC7C;2DADS;;;;;;;;;;;;;;;;;;;;;;+BAjEf;;;;;wBA0EX;;;;;;;;;;;;;;;;;;;;;;AAMZ", "debugId": null}}, {"offset": {"line": 2759, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/about/skills.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from \"@/components/ui/card\";\nimport { Progress } from \"@/components/ui/progress\";\n\nconst skillCategories = [\n  {\n    title: \"Frontend Development\",\n    skills: [\n      { name: \"React/Next.js\", level: 95, color: \"bg-blue-500\" },\n      { name: \"TypeScript\", level: 90, color: \"bg-blue-600\" },\n      { name: \"Tailwind CSS\", level: 92, color: \"bg-cyan-500\" },\n      { name: \"JavaScript (ES6+)\", level: 88, color: \"bg-yellow-500\" },\n      { name: \"HTML5 & CSS3\", level: 95, color: \"bg-orange-500\" },\n      { name: \"Framer Motion\", level: 85, color: \"bg-purple-500\" },\n    ]\n  },\n  {\n    title: \"Backend Development\",\n    skills: [\n      { name: \"Node.js\", level: 88, color: \"bg-green-600\" },\n      { name: \"Express.js\", level: 85, color: \"bg-gray-600\" },\n      { name: \"PostgreSQL\", level: 82, color: \"bg-blue-800\" },\n      { name: \"MongoDB\", level: 80, color: \"bg-green-500\" },\n      { name: \"Prisma ORM\", level: 85, color: \"bg-indigo-600\" },\n      { name: \"REST APIs\", level: 90, color: \"bg-teal-500\" },\n    ]\n  },\n  {\n    title: \"Tools & Technologies\",\n    skills: [\n      { name: \"Git & GitHub\", level: 92, color: \"bg-gray-800\" },\n      { name: \"Docker\", level: 75, color: \"bg-blue-500\" },\n      { name: \"AWS\", level: 70, color: \"bg-orange-500\" },\n      { name: \"Vercel\", level: 88, color: \"bg-black\" },\n      { name: \"Figma\", level: 85, color: \"bg-purple-500\" },\n      { name: \"VS Code\", level: 95, color: \"bg-blue-600\" },\n    ]\n  },\n  {\n    title: \"Soft Skills\",\n    skills: [\n      { name: \"Problem Solving\", level: 92, color: \"bg-emerald-500\" },\n      { name: \"Team Leadership\", level: 85, color: \"bg-rose-500\" },\n      { name: \"Communication\", level: 88, color: \"bg-amber-500\" },\n      { name: \"Project Management\", level: 80, color: \"bg-violet-500\" },\n      { name: \"Mentoring\", level: 82, color: \"bg-pink-500\" },\n      { name: \"Adaptability\", level: 90, color: \"bg-sky-500\" },\n    ]\n  }\n];\n\nexport function Skills() {\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  return (\n    <section ref={ref} className=\"py-20 bg-muted/30\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n        >\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n            <span className=\"gradient-text\">Skills & Expertise</span>\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            A comprehensive overview of my technical skills and proficiency levels across different technologies and domains.\n          </p>\n        </motion.div>\n\n        <div className=\"grid md:grid-cols-2 gap-8\">\n          {skillCategories.map((category, categoryIndex) => (\n            <motion.div\n              key={category.title}\n              initial={{ opacity: 0, y: 50 }}\n              animate={inView ? { opacity: 1, y: 0 } : {}}\n              transition={{ duration: 0.8, delay: categoryIndex * 0.1 }}\n            >\n              <Card className=\"h-full hover-lift border-border/50 hover:border-border transition-all duration-300\">\n                <CardHeader>\n                  <CardTitle className=\"text-xl font-bold\">{category.title}</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"space-y-6\">\n                    {category.skills.map((skill, skillIndex) => (\n                      <motion.div\n                        key={skill.name}\n                        className=\"space-y-2\"\n                        initial={{ opacity: 0, x: -20 }}\n                        animate={inView ? { opacity: 1, x: 0 } : {}}\n                        transition={{ \n                          duration: 0.5, \n                          delay: categoryIndex * 0.1 + skillIndex * 0.05 \n                        }}\n                      >\n                        <div className=\"flex justify-between items-center\">\n                          <span className=\"font-medium text-sm\">{skill.name}</span>\n                          <span className=\"text-sm text-muted-foreground\">{skill.level}%</span>\n                        </div>\n                        <div className=\"relative\">\n                          <div className=\"w-full bg-muted rounded-full h-2\">\n                            <motion.div\n                              className={`h-2 rounded-full ${skill.color}`}\n                              initial={{ width: 0 }}\n                              animate={inView ? { width: `${skill.level}%` } : { width: 0 }}\n                              transition={{ \n                                duration: 1, \n                                delay: categoryIndex * 0.1 + skillIndex * 0.05 + 0.2,\n                                ease: \"easeOut\"\n                              }}\n                            />\n                          </div>\n                        </div>\n                      </motion.div>\n                    ))}\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Additional Skills Summary */}\n        <motion.div\n          className=\"mt-16 text-center\"\n          initial={{ opacity: 0, y: 30 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.6 }}\n        >\n          <div className=\"bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl p-8 border border-border/50\">\n            <h3 className=\"text-2xl font-bold mb-4\">Continuous Learning</h3>\n            <p className=\"text-muted-foreground mb-6 max-w-2xl mx-auto\">\n              I'm passionate about staying up-to-date with the latest technologies and best practices. \n              Currently exploring AI/ML integration in web applications and advanced React patterns.\n            </p>\n            <div className=\"flex flex-wrap justify-center gap-4\">\n              {[\"AI/ML\", \"Web3\", \"GraphQL\", \"Microservices\", \"DevOps\", \"Mobile Development\"].map((skill) => (\n                <span \n                  key={skill}\n                  className=\"px-3 py-1 bg-background border border-border rounded-full text-sm font-medium\"\n                >\n                  {skill}\n                </span>\n              ))}\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAOA,MAAM,kBAAkB;IACtB;QACE,OAAO;QACP,QAAQ;YACN;gBAAE,MAAM;gBAAiB,OAAO;gBAAI,OAAO;YAAc;YACzD;gBAAE,MAAM;gBAAc,OAAO;gBAAI,OAAO;YAAc;YACtD;gBAAE,MAAM;gBAAgB,OAAO;gBAAI,OAAO;YAAc;YACxD;gBAAE,MAAM;gBAAqB,OAAO;gBAAI,OAAO;YAAgB;YAC/D;gBAAE,MAAM;gBAAgB,OAAO;gBAAI,OAAO;YAAgB;YAC1D;gBAAE,MAAM;gBAAiB,OAAO;gBAAI,OAAO;YAAgB;SAC5D;IACH;IACA;QACE,OAAO;QACP,QAAQ;YACN;gBAAE,MAAM;gBAAW,OAAO;gBAAI,OAAO;YAAe;YACpD;gBAAE,MAAM;gBAAc,OAAO;gBAAI,OAAO;YAAc;YACtD;gBAAE,MAAM;gBAAc,OAAO;gBAAI,OAAO;YAAc;YACtD;gBAAE,MAAM;gBAAW,OAAO;gBAAI,OAAO;YAAe;YACpD;gBAAE,MAAM;gBAAc,OAAO;gBAAI,OAAO;YAAgB;YACxD;gBAAE,MAAM;gBAAa,OAAO;gBAAI,OAAO;YAAc;SACtD;IACH;IACA;QACE,OAAO;QACP,QAAQ;YACN;gBAAE,MAAM;gBAAgB,OAAO;gBAAI,OAAO;YAAc;YACxD;gBAAE,MAAM;gBAAU,OAAO;gBAAI,OAAO;YAAc;YAClD;gBAAE,MAAM;gBAAO,OAAO;gBAAI,OAAO;YAAgB;YACjD;gBAAE,MAAM;gBAAU,OAAO;gBAAI,OAAO;YAAW;YAC/C;gBAAE,MAAM;gBAAS,OAAO;gBAAI,OAAO;YAAgB;YACnD;gBAAE,MAAM;gBAAW,OAAO;gBAAI,OAAO;YAAc;SACpD;IACH;IACA;QACE,OAAO;QACP,QAAQ;YACN;gBAAE,MAAM;gBAAmB,OAAO;gBAAI,OAAO;YAAiB;YAC9D;gBAAE,MAAM;gBAAmB,OAAO;gBAAI,OAAO;YAAc;YAC3D;gBAAE,MAAM;gBAAiB,OAAO;gBAAI,OAAO;YAAe;YAC1D;gBAAE,MAAM;gBAAsB,OAAO;gBAAI,OAAO;YAAgB;YAChE;gBAAE,MAAM;gBAAa,OAAO;gBAAI,OAAO;YAAc;YACrD;gBAAE,MAAM;gBAAgB,OAAO;gBAAI,OAAO;YAAa;SACxD;IACH;CACD;AAEM,SAAS;IACd,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,qBACE,8OAAC;QAAQ,KAAK;QAAK,WAAU;kBAC3B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAKjE,8OAAC;oBAAI,WAAU;8BACZ,gBAAgB,GAAG,CAAC,CAAC,UAAU,8BAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO,gBAAgB;4BAAI;sCAExD,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAqB,SAAS,KAAK;;;;;;;;;;;kDAE1D,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,2BAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,WAAU;oDACV,SAAS;wDAAE,SAAS;wDAAG,GAAG,CAAC;oDAAG;oDAC9B,SAAS,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE,IAAI,CAAC;oDAC1C,YAAY;wDACV,UAAU;wDACV,OAAO,gBAAgB,MAAM,aAAa;oDAC5C;;sEAEA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAuB,MAAM,IAAI;;;;;;8EACjD,8OAAC;oEAAK,WAAU;;wEAAiC,MAAM,KAAK;wEAAC;;;;;;;;;;;;;sEAE/D,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oEACT,WAAW,CAAC,iBAAiB,EAAE,MAAM,KAAK,EAAE;oEAC5C,SAAS;wEAAE,OAAO;oEAAE;oEACpB,SAAS,SAAS;wEAAE,OAAO,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC;oEAAC,IAAI;wEAAE,OAAO;oEAAE;oEAC5D,YAAY;wEACV,UAAU;wEACV,OAAO,gBAAgB,MAAM,aAAa,OAAO;wEACjD,MAAM;oEACR;;;;;;;;;;;;;;;;;mDAvBD,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;2BAbpB,SAAS,KAAK;;;;;;;;;;8BAkDzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BAExC,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAI5D,8OAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAS;oCAAQ;oCAAW;oCAAiB;oCAAU;iCAAqB,CAAC,GAAG,CAAC,CAAC,sBAClF,8OAAC;wCAEC,WAAU;kDAET;uCAHI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYvB", "debugId": null}}, {"offset": {"line": 3195, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/about/education.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { GraduationCap, Award, BookOpen, Calendar, School, Trophy } from \"lucide-react\";\nimport { VerticalTimeline, VerticalTimelineElement } from 'react-vertical-timeline-component';\nimport 'react-vertical-timeline-component/style.min.css';\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\n\nconst education = [\n  {\n    degree: \"Bachelor of Engineering in Computer Science\",\n    institution: \"Chandigarh University\",\n    location: \"Chandigarh, India\",\n    period: \"2020 - 2024\",\n    grade: \"First Class with Distinction (8.5/10 CGPA)\",\n    description: \"Focused on software engineering, data structures, algorithms, and web technologies. Completed final year project on e-commerce platform development.\",\n    highlights: [\n      \"Dean's List for 3 consecutive semesters\",\n      \"Led university coding club with 200+ members\",\n      \"Won inter-college hackathon for innovative web solution\",\n      \"Published research paper on web performance optimization\"\n    ],\n    icon: GraduationCap,\n    iconBg: \"#3b82f6\",\n    date: \"2016 - 2020\"\n  },\n  {\n    degree: \"Higher Secondary Certificate (Science)\",\n    institution: \"St. Xavier's College\",\n    location: \"Mumbai, India\",\n    period: \"2014 - 2016\",\n    grade: \"92.5%\",\n    description: \"Specialized in Mathematics, Physics, and Chemistry with additional focus on computer science fundamentals.\",\n    highlights: [\n      \"School topper in Computer Science\",\n      \"Represented school in state-level science exhibition\",\n      \"Active member of robotics club\"\n    ],\n    icon: School,\n    iconBg: \"#10b981\",\n    date: \"2014 - 2016\"\n  }\n];\n\nconst certifications = [\n  {\n    title: \"AWS Certified Solutions Architect\",\n    issuer: \"Amazon Web Services\",\n    date: \"2023\",\n    credentialId: \"AWS-CSA-2023-001\",\n    emoji: \"☁️\",\n    icon: Award,\n    iconBg: \"#f59e0b\",\n    description: \"Comprehensive certification covering AWS architecture best practices, security, and scalability.\"\n  },\n  {\n    title: \"React Developer Certification\",\n    issuer: \"Meta (Facebook)\",\n    date: \"2022\",\n    credentialId: \"META-REACT-2022-456\",\n    emoji: \"⚛️\",\n    icon: Trophy,\n    iconBg: \"#3b82f6\",\n    description: \"Advanced React development patterns, hooks, and modern React ecosystem.\"\n  },\n  {\n    title: \"Google Analytics Certified\",\n    issuer: \"Google\",\n    date: \"2022\",\n    credentialId: \"GOOGLE-GA-2022-789\",\n    emoji: \"📊\",\n    icon: Award,\n    iconBg: \"#10b981\",\n    description: \"Digital analytics, data interpretation, and conversion optimization strategies.\"\n  },\n  {\n    title: \"MongoDB Developer Certification\",\n    issuer: \"MongoDB University\",\n    date: \"2021\",\n    credentialId: \"MONGO-DEV-2021-123\",\n    emoji: \"🍃\",\n    icon: Trophy,\n    iconBg: \"#8b5cf6\",\n    description: \"NoSQL database design, aggregation pipelines, and performance optimization.\"\n  }\n];\n\nconst courses = [\n  \"Advanced React Patterns - Kent C. Dodds\",\n  \"TypeScript Masterclass - Marius Schulz\",\n  \"Node.js Design Patterns - Mario Casciaro\",\n  \"System Design Interview - Alex Xu\",\n  \"AWS Solutions Architecture - A Cloud Guru\",\n  \"UI/UX Design Fundamentals - Google UX\"\n];\n\nexport function Education() {\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  return (\n    <section ref={ref} className=\"py-20\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n        >\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n            <span className=\"gradient-text\">Education & Learning</span>\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            My educational background and continuous learning journey in technology and software development.\n          </p>\n        </motion.div>\n\n        {/* Formal Education */}\n        <div className=\"mb-16\">\n          <motion.h3\n            className=\"text-2xl font-bold mb-8 flex items-center\"\n            initial={{ opacity: 0, x: -20 }}\n            animate={inView ? { opacity: 1, x: 0 } : {}}\n            transition={{ duration: 0.6, delay: 0.2 }}\n          >\n            <GraduationCap className=\"mr-3 h-6 w-6 text-primary\" />\n            Formal Education\n          </motion.h3>\n\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={inView ? { opacity: 1 } : {}}\n            transition={{ duration: 0.8, delay: 0.3 }}\n          >\n            <VerticalTimeline>\n              {education.map((edu, index) => {\n                const Icon = edu.icon;\n                return (\n                  <VerticalTimelineElement\n                    key={index}\n                    className=\"vertical-timeline-element--education\"\n                    contentStyle={{\n                      background: 'hsl(var(--card))',\n                      color: 'hsl(var(--card-foreground))',\n                      border: '1px solid hsl(var(--border))',\n                      borderRadius: '12px',\n                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n                    }}\n                    contentArrowStyle={{\n                      borderRight: '7px solid hsl(var(--border))',\n                    }}\n                    date={edu.date}\n                    iconStyle={{\n                      background: edu.iconBg,\n                      color: '#fff',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                    }}\n                    icon={<Icon className=\"h-6 w-6\" />}\n                  >\n                    <div className=\"space-y-4\">\n                      <div>\n                        <h3 className=\"text-xl font-bold\">{edu.degree}</h3>\n                        <div className=\"text-primary font-medium\">{edu.institution}</div>\n                        <div className=\"text-sm text-muted-foreground\">{edu.location}</div>\n                        <Badge variant=\"secondary\" className=\"mt-2\">{edu.grade}</Badge>\n                      </div>\n\n                      <p className=\"text-muted-foreground\">{edu.description}</p>\n\n                      <div>\n                        <h4 className=\"font-semibold mb-2\">Key Highlights:</h4>\n                        <ul className=\"space-y-1\">\n                          {edu.highlights.map((highlight, highlightIndex) => (\n                            <li key={highlightIndex} className=\"flex items-start space-x-2 text-sm text-muted-foreground\">\n                              <div className=\"w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0\" />\n                              <span>{highlight}</span>\n                            </li>\n                          ))}\n                        </ul>\n                      </div>\n                    </div>\n                  </VerticalTimelineElement>\n                );\n              })}\n            </VerticalTimeline>\n          </motion.div>\n        </div>\n\n        {/* Certifications */}\n        <div className=\"mb-16\">\n          <motion.h3\n            className=\"text-2xl font-bold mb-8 flex items-center\"\n            initial={{ opacity: 0, x: -20 }}\n            animate={inView ? { opacity: 1, x: 0 } : {}}\n            transition={{ duration: 0.6, delay: 0.4 }}\n          >\n            <Award className=\"mr-3 h-6 w-6 text-primary\" />\n            Certifications\n          </motion.h3>\n\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={inView ? { opacity: 1 } : {}}\n            transition={{ duration: 0.8, delay: 0.5 }}\n          >\n            <VerticalTimeline>\n              {certifications.map((cert, index) => {\n                const Icon = cert.icon;\n                return (\n                  <VerticalTimelineElement\n                    key={index}\n                    className=\"vertical-timeline-element--certification\"\n                    contentStyle={{\n                      background: 'hsl(var(--card))',\n                      color: 'hsl(var(--card-foreground))',\n                      border: '1px solid hsl(var(--border))',\n                      borderRadius: '12px',\n                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n                    }}\n                    contentArrowStyle={{\n                      borderRight: '7px solid hsl(var(--border))',\n                    }}\n                    date={cert.date}\n                    iconStyle={{\n                      background: cert.iconBg,\n                      color: '#fff',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                    }}\n                    icon={<Icon className=\"h-5 w-5\" />}\n                  >\n                    <div className=\"space-y-3\">\n                      <div className=\"flex items-start space-x-3\">\n                        <div className=\"text-2xl\">{cert.emoji}</div>\n                        <div className=\"flex-1\">\n                          <h4 className=\"font-bold text-lg\">{cert.title}</h4>\n                          <p className=\"text-primary font-medium\">{cert.issuer}</p>\n                          <p className=\"text-sm text-muted-foreground mt-1\">{cert.description}</p>\n                          <p className=\"text-xs text-muted-foreground mt-2\">\n                            Credential ID: {cert.credentialId}\n                          </p>\n                        </div>\n                      </div>\n                    </div>\n                  </VerticalTimelineElement>\n                );\n              })}\n            </VerticalTimeline>\n          </motion.div>\n        </div>\n\n        {/* Continuous Learning */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.6, delay: 0.6 }}\n        >\n          <h3 className=\"text-2xl font-bold mb-8 flex items-center\">\n            <BookOpen className=\"mr-3 h-6 w-6 text-primary\" />\n            Continuous Learning\n          </h3>\n          \n          <Card className=\"border-border/50\">\n            <CardHeader>\n              <CardTitle>Recent Courses & Training</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid md:grid-cols-2 gap-4\">\n                {courses.map((course, index) => (\n                  <motion.div\n                    key={index}\n                    className=\"flex items-center space-x-3 p-3 rounded-lg bg-muted/50 hover:bg-muted transition-colors duration-200\"\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={inView ? { opacity: 1, x: 0 } : {}}\n                    transition={{ duration: 0.4, delay: 0.7 + index * 0.05 }}\n                  >\n                    <div className=\"w-2 h-2 rounded-full bg-primary flex-shrink-0\" />\n                    <span className=\"text-sm font-medium\">{course}</span>\n                  </motion.div>\n                ))}\n              </div>\n              \n              <div className=\"mt-6 p-4 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg border border-border/50\">\n                <p className=\"text-sm text-muted-foreground\">\n                  <strong>Learning Philosophy:</strong> I believe in continuous learning and staying updated with the latest \n                  technologies. I dedicate at least 5 hours per week to learning new skills and exploring emerging trends \n                  in web development and software engineering.\n                </p>\n              </div>\n            </CardContent>\n          </Card>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AARA;;;;;;;;;AAUA,MAAM,YAAY;IAChB;QACE,QAAQ;QACR,aAAa;QACb,UAAU;QACV,QAAQ;QACR,OAAO;QACP,aAAa;QACb,YAAY;YACV;YACA;YACA;YACA;SACD;QACD,MAAM,wNAAA,CAAA,gBAAa;QACnB,QAAQ;QACR,MAAM;IACR;IACA;QACE,QAAQ;QACR,aAAa;QACb,UAAU;QACV,QAAQ;QACR,OAAO;QACP,aAAa;QACb,YAAY;YACV;YACA;YACA;SACD;QACD,MAAM,sMAAA,CAAA,SAAM;QACZ,QAAQ;QACR,MAAM;IACR;CACD;AAED,MAAM,iBAAiB;IACrB;QACE,OAAO;QACP,QAAQ;QACR,MAAM;QACN,cAAc;QACd,OAAO;QACP,MAAM,oMAAA,CAAA,QAAK;QACX,QAAQ;QACR,aAAa;IACf;IACA;QACE,OAAO;QACP,QAAQ;QACR,MAAM;QACN,cAAc;QACd,OAAO;QACP,MAAM,sMAAA,CAAA,SAAM;QACZ,QAAQ;QACR,aAAa;IACf;IACA;QACE,OAAO;QACP,QAAQ;QACR,MAAM;QACN,cAAc;QACd,OAAO;QACP,MAAM,oMAAA,CAAA,QAAK;QACX,QAAQ;QACR,aAAa;IACf;IACA;QACE,OAAO;QACP,QAAQ;QACR,MAAM;QACN,cAAc;QACd,OAAO;QACP,MAAM,sMAAA,CAAA,SAAM;QACZ,QAAQ;QACR,aAAa;IACf;CACD;AAED,MAAM,UAAU;IACd;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAAS;IACd,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,qBACE,8OAAC;QAAQ,KAAK;QAAK,WAAU;kBAC3B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAMjE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;;8CAExC,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAA8B;;;;;;;sCAIzD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS,SAAS;gCAAE,SAAS;4BAAE,IAAI,CAAC;4BACpC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAExC,cAAA,8OAAC,kLAAA,CAAA,mBAAgB;0CACd,UAAU,GAAG,CAAC,CAAC,KAAK;oCACnB,MAAM,OAAO,IAAI,IAAI;oCACrB,qBACE,8OAAC,kLAAA,CAAA,0BAAuB;wCAEtB,WAAU;wCACV,cAAc;4CACZ,YAAY;4CACZ,OAAO;4CACP,QAAQ;4CACR,cAAc;4CACd,WAAW;wCACb;wCACA,mBAAmB;4CACjB,aAAa;wCACf;wCACA,MAAM,IAAI,IAAI;wCACd,WAAW;4CACT,YAAY,IAAI,MAAM;4CACtB,OAAO;4CACP,SAAS;4CACT,YAAY;4CACZ,gBAAgB;wCAClB;wCACA,oBAAM,8OAAC;4CAAK,WAAU;;;;;;kDAEtB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAqB,IAAI,MAAM;;;;;;sEAC7C,8OAAC;4DAAI,WAAU;sEAA4B,IAAI,WAAW;;;;;;sEAC1D,8OAAC;4DAAI,WAAU;sEAAiC,IAAI,QAAQ;;;;;;sEAC5D,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAU;sEAAQ,IAAI,KAAK;;;;;;;;;;;;8DAGxD,8OAAC;oDAAE,WAAU;8DAAyB,IAAI,WAAW;;;;;;8DAErD,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAqB;;;;;;sEACnC,8OAAC;4DAAG,WAAU;sEACX,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,+BAC9B,8OAAC;oEAAwB,WAAU;;sFACjC,8OAAC;4EAAI,WAAU;;;;;;sFACf,8OAAC;sFAAM;;;;;;;mEAFA;;;;;;;;;;;;;;;;;;;;;;uCApCZ;;;;;gCA8CX;;;;;;;;;;;;;;;;;8BAMN,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;;8CAExC,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAA8B;;;;;;;sCAIjD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS,SAAS;gCAAE,SAAS;4BAAE,IAAI,CAAC;4BACpC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAExC,cAAA,8OAAC,kLAAA,CAAA,mBAAgB;0CACd,eAAe,GAAG,CAAC,CAAC,MAAM;oCACzB,MAAM,OAAO,KAAK,IAAI;oCACtB,qBACE,8OAAC,kLAAA,CAAA,0BAAuB;wCAEtB,WAAU;wCACV,cAAc;4CACZ,YAAY;4CACZ,OAAO;4CACP,QAAQ;4CACR,cAAc;4CACd,WAAW;wCACb;wCACA,mBAAmB;4CACjB,aAAa;wCACf;wCACA,MAAM,KAAK,IAAI;wCACf,WAAW;4CACT,YAAY,KAAK,MAAM;4CACvB,OAAO;4CACP,SAAS;4CACT,YAAY;4CACZ,gBAAgB;wCAClB;wCACA,oBAAM,8OAAC;4CAAK,WAAU;;;;;;kDAEtB,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAY,KAAK,KAAK;;;;;;kEACrC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAqB,KAAK,KAAK;;;;;;0EAC7C,8OAAC;gEAAE,WAAU;0EAA4B,KAAK,MAAM;;;;;;0EACpD,8OAAC;gEAAE,WAAU;0EAAsC,KAAK,WAAW;;;;;;0EACnE,8OAAC;gEAAE,WAAU;;oEAAqC;oEAChC,KAAK,YAAY;;;;;;;;;;;;;;;;;;;;;;;;uCA9BpC;;;;;gCAqCX;;;;;;;;;;;;;;;;;8BAMN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;;sCAExC,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAA8B;;;;;;;sCAIpD,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;8CAEb,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,WAAU;oDACV,SAAS;wDAAE,SAAS;wDAAG,GAAG,CAAC;oDAAG;oDAC9B,SAAS,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE,IAAI,CAAC;oDAC1C,YAAY;wDAAE,UAAU;wDAAK,OAAO,MAAM,QAAQ;oDAAK;;sEAEvD,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAAuB;;;;;;;mDAPlC;;;;;;;;;;sDAYX,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;kEAAO;;;;;;oDAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWvD", "debugId": null}}]}