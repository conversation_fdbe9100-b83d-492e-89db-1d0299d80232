/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { groupsmigration_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof groupsmigration_v1.Groupsmigration;
};
export declare function groupsmigration(version: 'v1'): groupsmigration_v1.Groupsmigration;
export declare function groupsmigration(options: groupsmigration_v1.Options): groupsmigration_v1.Groupsmigration;
declare const auth: AuthPlus;
export { auth };
export { groupsmigration_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
