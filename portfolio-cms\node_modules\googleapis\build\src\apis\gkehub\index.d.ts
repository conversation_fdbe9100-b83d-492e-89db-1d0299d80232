/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { gkehub_v1 } from './v1';
import { gkehub_v1alpha } from './v1alpha';
import { gkehub_v1alpha2 } from './v1alpha2';
import { gkehub_v1beta } from './v1beta';
import { gkehub_v1beta1 } from './v1beta1';
import { gkehub_v2 } from './v2';
import { gkehub_v2alpha } from './v2alpha';
import { gkehub_v2beta } from './v2beta';
export declare const VERSIONS: {
    v1: typeof gkehub_v1.Gkehub;
    v1alpha: typeof gkehub_v1alpha.Gkehub;
    v1alpha2: typeof gkehub_v1alpha2.Gkehub;
    v1beta: typeof gkehub_v1beta.Gkehub;
    v1beta1: typeof gkehub_v1beta1.Gkehub;
    v2: typeof gkehub_v2.Gkehub;
    v2alpha: typeof gkehub_v2alpha.Gkehub;
    v2beta: typeof gkehub_v2beta.Gkehub;
};
export declare function gkehub(version: 'v1'): gkehub_v1.Gkehub;
export declare function gkehub(options: gkehub_v1.Options): gkehub_v1.Gkehub;
export declare function gkehub(version: 'v1alpha'): gkehub_v1alpha.Gkehub;
export declare function gkehub(options: gkehub_v1alpha.Options): gkehub_v1alpha.Gkehub;
export declare function gkehub(version: 'v1alpha2'): gkehub_v1alpha2.Gkehub;
export declare function gkehub(options: gkehub_v1alpha2.Options): gkehub_v1alpha2.Gkehub;
export declare function gkehub(version: 'v1beta'): gkehub_v1beta.Gkehub;
export declare function gkehub(options: gkehub_v1beta.Options): gkehub_v1beta.Gkehub;
export declare function gkehub(version: 'v1beta1'): gkehub_v1beta1.Gkehub;
export declare function gkehub(options: gkehub_v1beta1.Options): gkehub_v1beta1.Gkehub;
export declare function gkehub(version: 'v2'): gkehub_v2.Gkehub;
export declare function gkehub(options: gkehub_v2.Options): gkehub_v2.Gkehub;
export declare function gkehub(version: 'v2alpha'): gkehub_v2alpha.Gkehub;
export declare function gkehub(options: gkehub_v2alpha.Options): gkehub_v2alpha.Gkehub;
export declare function gkehub(version: 'v2beta'): gkehub_v2beta.Gkehub;
export declare function gkehub(options: gkehub_v2beta.Options): gkehub_v2beta.Gkehub;
declare const auth: AuthPlus;
export { auth };
export { gkehub_v1 };
export { gkehub_v1alpha };
export { gkehub_v1alpha2 };
export { gkehub_v1beta };
export { gkehub_v1beta1 };
export { gkehub_v2 };
export { gkehub_v2alpha };
export { gkehub_v2beta };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
