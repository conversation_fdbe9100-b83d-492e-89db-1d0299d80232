/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { playintegrity_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof playintegrity_v1.Playintegrity;
};
export declare function playintegrity(version: 'v1'): playintegrity_v1.Playintegrity;
export declare function playintegrity(options: playintegrity_v1.Options): playintegrity_v1.Playintegrity;
declare const auth: AuthPlus;
export { auth };
export { playintegrity_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
