/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { lifesciences_v2beta } from './v2beta';
export declare const VERSIONS: {
    v2beta: typeof lifesciences_v2beta.Lifesciences;
};
export declare function lifesciences(version: 'v2beta'): lifesciences_v2beta.Lifesciences;
export declare function lifesciences(options: lifesciences_v2beta.Options): lifesciences_v2beta.Lifesciences;
declare const auth: AuthPlus;
export { auth };
export { lifesciences_v2beta };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
