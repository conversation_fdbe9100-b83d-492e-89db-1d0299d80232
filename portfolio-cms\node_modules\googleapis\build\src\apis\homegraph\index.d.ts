/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { homegraph_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof homegraph_v1.Homegraph;
};
export declare function homegraph(version: 'v1'): homegraph_v1.Homegraph;
export declare function homegraph(options: homegraph_v1.Options): homegraph_v1.Homegraph;
declare const auth: AuthPlus;
export { auth };
export { homegraph_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
