"use client";

import Link from "next/link";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { Calendar, Clock, ArrowRight } from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

interface BlogPost {
  id: number;
  title: string;
  excerpt: string;
  slug: string;
  category: string;
  tags: string[];
  publishedAt: string;
  readTime: string;
  views: number;
}

interface RelatedPostsProps {
  currentPost: BlogPost;
}

// Mock related posts - in a real app, this would come from an API
const getRelatedPosts = (currentPost: BlogPost): BlogPost[] => {
  const allPosts: BlogPost[] = [
    {
      id: 2,
      title: "Next.js 14 App Router: Complete Guide",
      excerpt: "Dive deep into Next.js 14's App Router, exploring new features, migration strategies, and performance optimizations.",
      slug: "nextjs-14-app-router-complete-guide",
      category: "Next.js",
      tags: ["Next.js", "React", "App Router", "Performance"],
      publishedAt: "2024-01-10",
      readTime: "12 min read",
      views: 980,
    },
    {
      id: 3,
      title: "Mastering CSS Grid and Flexbox in 2024",
      excerpt: "A comprehensive guide to modern CSS layout techniques, comparing Grid and Flexbox with practical examples and use cases.",
      slug: "mastering-css-grid-flexbox-2024",
      category: "CSS",
      tags: ["CSS", "Grid", "Flexbox", "Layout"],
      publishedAt: "2024-01-05",
      readTime: "10 min read",
      views: 750,
    },
    {
      id: 4,
      title: "State Management in React: Redux vs Zustand vs Context",
      excerpt: "Compare different state management solutions for React applications and learn when to use each approach.",
      slug: "react-state-management-redux-zustand-context",
      category: "React",
      tags: ["React", "State Management", "Redux", "Zustand"],
      publishedAt: "2023-12-28",
      readTime: "15 min read",
      views: 1100,
    }
  ];

  // Filter out current post and return posts with similar tags or category
  return allPosts
    .filter(post => post.id !== currentPost.id)
    .filter(post => 
      post.category === currentPost.category || 
      post.tags.some(tag => currentPost.tags.includes(tag))
    )
    .slice(0, 3);
};

export function RelatedPosts({ currentPost }: RelatedPostsProps) {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const relatedPosts = getRelatedPosts(currentPost);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (relatedPosts.length === 0) {
    return null;
  }

  return (
    <section ref={ref} className="py-20 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-3xl font-bold mb-4">
            <span className="gradient-text">Related Articles</span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Continue your learning journey with these related articles
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {relatedPosts.map((post, index) => (
            <motion.div
              key={post.id}
              initial={{ opacity: 0, y: 30 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <Card className="h-full hover-lift group border-border/50 hover:border-border transition-all duration-300">
                <div className="relative h-40 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-t-lg">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-4xl opacity-20">📄</div>
                  </div>
                  <div className="absolute top-3 right-3">
                    <Badge variant="secondary" className="text-xs">
                      {post.category}
                    </Badge>
                  </div>
                </div>
                
                <CardHeader className="pb-2">
                  <div className="flex items-center space-x-3 text-xs text-muted-foreground mb-2">
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-3 w-3" />
                      <span>{formatDate(post.publishedAt)}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="h-3 w-3" />
                      <span>{post.readTime}</span>
                    </div>
                  </div>
                  <CardTitle className="text-lg group-hover:text-primary transition-colors duration-300 line-clamp-2">
                    {post.title}
                  </CardTitle>
                </CardHeader>
                
                <CardContent className="flex-1 flex flex-col">
                  <p className="text-sm text-muted-foreground mb-4 line-clamp-3 flex-1">
                    {post.excerpt}
                  </p>
                  
                  <div className="flex flex-wrap gap-1 mb-4">
                    {post.tags.slice(0, 2).map((tag) => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {post.tags.length > 2 && (
                      <Badge variant="outline" className="text-xs">
                        +{post.tags.length - 2}
                      </Badge>
                    )}
                  </div>
                  
                  <Button asChild variant="ghost" className="group p-0 h-auto justify-start">
                    <Link href={`/blog/${post.slug}`}>
                      Read Article
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* More Articles CTA */}
        <motion.div
          className="text-center mt-12"
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <Button asChild size="lg" className="group">
            <Link href="/blog">
              View All Articles
              <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
            </Link>
          </Button>
        </motion.div>
      </div>
    </section>
  );
}
