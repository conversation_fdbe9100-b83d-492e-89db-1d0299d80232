import { <PERSON>ada<PERSON> } from "next";
import { notFound } from "next/navigation";
import { Navigation } from "@/components/navigation";
import { Footer } from "@/components/footer";
import { BlogPostHeader } from "@/components/blog/blog-post-header";
import { BlogPostContent } from "@/components/blog/blog-post-content";
import { BlogPostSidebar } from "@/components/blog/blog-post-sidebar";
import { RelatedPosts } from "@/components/blog/related-posts";
import { BlogPostNavigation } from "@/components/blog/blog-post-navigation";

// This would typically come from a CMS or database
const getBlogPost = async (slug: string) => {
  const blogPosts = [
    {
      id: 1,
      title: "Building Scalable React Applications with TypeScript",
      excerpt: "Learn how to structure large React applications using TypeScript, advanced patterns, and best practices for maintainability and performance.",
      content: `
# Building Scalable React Applications with TypeScript

Building scalable React applications is one of the most important skills for modern frontend developers. In this comprehensive guide, we'll explore how to structure large React applications using TypeScript, advanced patterns, and best practices.

## Why TypeScript for React?

TypeScript brings several advantages to React development:

- **Type Safety**: Catch errors at compile time rather than runtime
- **Better IDE Support**: Enhanced autocomplete, refactoring, and navigation
- **Self-Documenting Code**: Types serve as inline documentation
- **Easier Refactoring**: Confident code changes with type checking

## Project Structure

A well-organized project structure is crucial for scalability:

\`\`\`
src/
├── components/
│   ├── ui/           # Reusable UI components
│   ├── forms/        # Form components
│   └── layout/       # Layout components
├── hooks/            # Custom React hooks
├── services/         # API services
├── types/            # TypeScript type definitions
├── utils/            # Utility functions
└── stores/           # State management
\`\`\`

## Component Design Patterns

### 1. Compound Components

Compound components allow you to create flexible and reusable component APIs:

\`\`\`typescript
interface TabsProps {
  children: React.ReactNode;
  defaultValue?: string;
}

export const Tabs = ({ children, defaultValue }: TabsProps) => {
  const [activeTab, setActiveTab] = useState(defaultValue);
  
  return (
    <TabsContext.Provider value={{ activeTab, setActiveTab }}>
      {children}
    </TabsContext.Provider>
  );
};

Tabs.List = TabsList;
Tabs.Trigger = TabsTrigger;
Tabs.Content = TabsContent;
\`\`\`

### 2. Render Props Pattern

The render props pattern provides flexibility in component composition:

\`\`\`typescript
interface DataFetcherProps<T> {
  url: string;
  children: (data: T | null, loading: boolean, error: Error | null) => React.ReactNode;
}

export const DataFetcher = <T,>({ url, children }: DataFetcherProps<T>) => {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Fetch logic here...

  return children(data, loading, error);
};
\`\`\`

## State Management

For large applications, consider these state management approaches:

### 1. Context + useReducer

For complex local state:

\`\`\`typescript
interface AppState {
  user: User | null;
  theme: 'light' | 'dark';
  notifications: Notification[];
}

type AppAction = 
  | { type: 'SET_USER'; payload: User }
  | { type: 'TOGGLE_THEME' }
  | { type: 'ADD_NOTIFICATION'; payload: Notification };

const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'SET_USER':
      return { ...state, user: action.payload };
    case 'TOGGLE_THEME':
      return { ...state, theme: state.theme === 'light' ? 'dark' : 'light' };
    default:
      return state;
  }
};
\`\`\`

### 2. Zustand for Global State

Zustand provides a simple and powerful state management solution:

\`\`\`typescript
interface UserStore {
  user: User | null;
  setUser: (user: User) => void;
  logout: () => void;
}

export const useUserStore = create<UserStore>((set) => ({
  user: null,
  setUser: (user) => set({ user }),
  logout: () => set({ user: null }),
}));
\`\`\`

## Performance Optimization

### 1. Code Splitting

Use React.lazy for component-level code splitting:

\`\`\`typescript
const LazyComponent = React.lazy(() => import('./LazyComponent'));

const App = () => (
  <Suspense fallback={<Loading />}>
    <LazyComponent />
  </Suspense>
);
\`\`\`

### 2. Memoization

Use React.memo and useMemo strategically:

\`\`\`typescript
const ExpensiveComponent = React.memo(({ data }: { data: ComplexData }) => {
  const processedData = useMemo(() => {
    return expensiveCalculation(data);
  }, [data]);

  return <div>{processedData}</div>;
});
\`\`\`

## Testing Strategies

### 1. Component Testing

Use React Testing Library for component tests:

\`\`\`typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from './Button';

test('calls onClick when clicked', () => {
  const handleClick = jest.fn();
  render(<Button onClick={handleClick}>Click me</Button>);
  
  fireEvent.click(screen.getByRole('button'));
  expect(handleClick).toHaveBeenCalledTimes(1);
});
\`\`\`

### 2. Custom Hook Testing

Test custom hooks with renderHook:

\`\`\`typescript
import { renderHook, act } from '@testing-library/react';
import { useCounter } from './useCounter';

test('increments counter', () => {
  const { result } = renderHook(() => useCounter());
  
  act(() => {
    result.current.increment();
  });
  
  expect(result.current.count).toBe(1);
});
\`\`\`

## Conclusion

Building scalable React applications with TypeScript requires careful planning, good architecture decisions, and adherence to best practices. By following the patterns and techniques outlined in this guide, you'll be well-equipped to build maintainable and performant React applications.

Remember that scalability is not just about code organization—it's also about team collaboration, documentation, and continuous improvement of your development processes.
      `,
      slug: "building-scalable-react-applications-typescript",
      category: "React",
      tags: ["React", "TypeScript", "Architecture", "Best Practices"],
      publishedAt: "2024-01-15",
      updatedAt: "2024-01-15",
      readTime: "8 min read",
      views: 1250,
      featured: true,
      author: {
        name: "Ashish Kamat",
        avatar: "/ashish-profile.svg",
        bio: "Full Stack Developer & UI/UX Designer"
      }
    },
    // Add more blog posts here...
  ];

  return blogPosts.find(post => post.slug === slug);
};

interface BlogPostPageProps {
  params: {
    slug: string;
  };
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const { slug } = await params;
  const post = await getBlogPost(slug);

  if (!post) {
    return {
      title: "Post Not Found",
    };
  }

  return {
    title: `${post.title} - Ashish Kamat`,
    description: post.excerpt,
    openGraph: {
      title: post.title,
      description: post.excerpt,
      type: "article",
      publishedTime: post.publishedAt,
      authors: [post.author.name],
      tags: post.tags,
    },
    twitter: {
      card: "summary_large_image",
      title: post.title,
      description: post.excerpt,
    },
  };
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { slug } = await params;
  const post = await getBlogPost(slug);

  if (!post) {
    notFound();
  }

  return (
    <div className="min-h-screen">
      <Navigation />
      <main className="pt-16">
        <article>
          <BlogPostHeader post={post} />
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="grid lg:grid-cols-4 gap-12">
              <div className="lg:col-span-3">
                <BlogPostContent post={post} />
                <BlogPostNavigation currentSlug={slug} />
              </div>
              <div className="lg:col-span-1">
                <BlogPostSidebar post={post} />
              </div>
            </div>
          </div>
        </article>
        <RelatedPosts currentPost={post} />
      </main>
      <Footer />
    </div>
  );
}
