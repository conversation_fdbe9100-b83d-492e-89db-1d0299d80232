/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { indexing_v3 } from './v3';
export declare const VERSIONS: {
    v3: typeof indexing_v3.Indexing;
};
export declare function indexing(version: 'v3'): indexing_v3.Indexing;
export declare function indexing(options: indexing_v3.Options): indexing_v3.Indexing;
declare const auth: AuthPlus;
export { auth };
export { indexing_v3 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
