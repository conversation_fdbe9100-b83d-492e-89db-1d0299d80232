pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*

Original style from softwaremaniacs.org (c) <PERSON> <<PERSON><PERSON>@SoftwareManiacs.Org>

*/
.hljs {
  background: white;
  color: black
}
.hljs-string,
.hljs-variable,
.hljs-template-variable,
.hljs-symbol,
.hljs-bullet,
.hljs-section,
.hljs-addition,
.hljs-attribute,
.hljs-link {
  color: #888
}
.hljs-comment,
.hljs-quote,
.hljs-meta,
.hljs-deletion {
  color: #ccc
}
.hljs-keyword,
.hljs-selector-tag,
.hljs-section,
.hljs-name,
.hljs-type,
.hljs-strong {
  font-weight: bold
}
.hljs-emphasis {
  font-style: italic
}