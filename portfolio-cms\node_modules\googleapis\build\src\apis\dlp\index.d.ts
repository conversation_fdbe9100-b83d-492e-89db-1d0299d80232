/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { dlp_v2 } from './v2';
export declare const VERSIONS: {
    v2: typeof dlp_v2.Dlp;
};
export declare function dlp(version: 'v2'): dlp_v2.Dlp;
export declare function dlp(options: dlp_v2.Options): dlp_v2.Dlp;
declare const auth: AuthPlus;
export { auth };
export { dlp_v2 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
