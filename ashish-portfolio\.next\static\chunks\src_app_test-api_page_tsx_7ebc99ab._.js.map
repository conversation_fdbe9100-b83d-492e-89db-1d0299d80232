{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/app/test-api/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n\nexport default function TestApiPage() {\n  const [data, setData] = useState<any>(null)\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  useEffect(() => {\n    const testApi = async () => {\n      try {\n        console.log('Testing API call to CMS...')\n        const response = await fetch('http://localhost:3001/api/projects')\n        console.log('Response status:', response.status)\n        console.log('Response headers:', response.headers)\n        \n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`)\n        }\n        \n        const result = await response.json()\n        console.log('API Response:', result)\n        setData(result)\n      } catch (err) {\n        console.error('API Error:', err)\n        setError(err instanceof Error ? err.message : 'Unknown error')\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    testApi()\n  }, [])\n\n  if (loading) return <div className=\"p-8\">Loading...</div>\n  if (error) return <div className=\"p-8 text-red-500\">Error: {error}</div>\n\n  return (\n    <div className=\"p-8\">\n      <h1 className=\"text-2xl font-bold mb-4\">API Test Results</h1>\n      <pre className=\"bg-gray-100 p-4 rounded overflow-auto\">\n        {JSON.stringify(data, null, 2)}\n      </pre>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACtC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;iDAAU;oBACd,IAAI;wBACF,QAAQ,GAAG,CAAC;wBACZ,MAAM,WAAW,MAAM,MAAM;wBAC7B,QAAQ,GAAG,CAAC,oBAAoB,SAAS,MAAM;wBAC/C,QAAQ,GAAG,CAAC,qBAAqB,SAAS,OAAO;wBAEjD,IAAI,CAAC,SAAS,EAAE,EAAE;4BAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;wBAC1D;wBAEA,MAAM,SAAS,MAAM,SAAS,IAAI;wBAClC,QAAQ,GAAG,CAAC,iBAAiB;wBAC7B,QAAQ;oBACV,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,cAAc;wBAC5B,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;oBAChD,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;gCAAG,EAAE;IAEL,IAAI,SAAS,qBAAO,6LAAC;QAAI,WAAU;kBAAM;;;;;;IACzC,IAAI,OAAO,qBAAO,6LAAC;QAAI,WAAU;;YAAmB;YAAQ;;;;;;;IAE5D,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA0B;;;;;;0BACxC,6LAAC;gBAAI,WAAU;0BACZ,KAAK,SAAS,CAAC,MAAM,MAAM;;;;;;;;;;;;AAIpC;GA1CwB;KAAA", "debugId": null}}]}