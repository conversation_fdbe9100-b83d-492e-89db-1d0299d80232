/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { analyticsreporting_v4 } from './v4';
export declare const VERSIONS: {
    v4: typeof analyticsreporting_v4.Analyticsreporting;
};
export declare function analyticsreporting(version: 'v4'): analyticsreporting_v4.Analyticsreporting;
export declare function analyticsreporting(options: analyticsreporting_v4.Options): analyticsreporting_v4.Analyticsreporting;
declare const auth: AuthPlus;
export { auth };
export { analyticsreporting_v4 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
