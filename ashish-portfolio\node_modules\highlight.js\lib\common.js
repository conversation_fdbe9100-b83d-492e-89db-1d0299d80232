var hljs = require('./core');

hljs.registerLanguage('xml', require('./languages/xml'));
hljs.registerLanguage('bash', require('./languages/bash'));
hljs.registerLanguage('c', require('./languages/c'));
hljs.registerLanguage('cpp', require('./languages/cpp'));
hljs.registerLanguage('csharp', require('./languages/csharp'));
hljs.registerLanguage('css', require('./languages/css'));
hljs.registerLanguage('markdown', require('./languages/markdown'));
hljs.registerLanguage('diff', require('./languages/diff'));
hljs.registerLanguage('ruby', require('./languages/ruby'));
hljs.registerLanguage('go', require('./languages/go'));
hljs.registerLanguage('graphql', require('./languages/graphql'));
hljs.registerLanguage('ini', require('./languages/ini'));
hljs.registerLanguage('java', require('./languages/java'));
hljs.registerLanguage('javascript', require('./languages/javascript'));
hljs.registerLanguage('json', require('./languages/json'));
hljs.registerLanguage('kotlin', require('./languages/kotlin'));
hljs.registerLanguage('less', require('./languages/less'));
hljs.registerLanguage('lua', require('./languages/lua'));
hljs.registerLanguage('makefile', require('./languages/makefile'));
hljs.registerLanguage('perl', require('./languages/perl'));
hljs.registerLanguage('objectivec', require('./languages/objectivec'));
hljs.registerLanguage('php', require('./languages/php'));
hljs.registerLanguage('php-template', require('./languages/php-template'));
hljs.registerLanguage('plaintext', require('./languages/plaintext'));
hljs.registerLanguage('python', require('./languages/python'));
hljs.registerLanguage('python-repl', require('./languages/python-repl'));
hljs.registerLanguage('r', require('./languages/r'));
hljs.registerLanguage('rust', require('./languages/rust'));
hljs.registerLanguage('scss', require('./languages/scss'));
hljs.registerLanguage('shell', require('./languages/shell'));
hljs.registerLanguage('sql', require('./languages/sql'));
hljs.registerLanguage('swift', require('./languages/swift'));
hljs.registerLanguage('yaml', require('./languages/yaml'));
hljs.registerLanguage('typescript', require('./languages/typescript'));
hljs.registerLanguage('vbnet', require('./languages/vbnet'));
hljs.registerLanguage('wasm', require('./languages/wasm'));

hljs.HighlightJS = hljs
hljs.default = hljs
module.exports = hljs;