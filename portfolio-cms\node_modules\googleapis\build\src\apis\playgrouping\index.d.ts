/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { playgrouping_v1alpha1 } from './v1alpha1';
export declare const VERSIONS: {
    v1alpha1: typeof playgrouping_v1alpha1.Playgrouping;
};
export declare function playgrouping(version: 'v1alpha1'): playgrouping_v1alpha1.Playgrouping;
export declare function playgrouping(options: playgrouping_v1alpha1.Options): playgrouping_v1alpha1.Playgrouping;
declare const auth: AuthPlus;
export { auth };
export { playgrouping_v1alpha1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
