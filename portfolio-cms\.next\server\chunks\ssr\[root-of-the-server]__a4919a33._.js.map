{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/portfolio-cms/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/portfolio-cms/src/components/dashboard/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { cn } from '@/lib/utils'\nimport {\n  LayoutDashboard,\n  FolderOpen,\n  FileText,\n  Briefcase,\n  MessageSquare,\n  Settings,\n  BarChart3,\n  Code,\n} from 'lucide-react'\n\nconst navigation = [\n  {\n    name: 'Dashboard',\n    href: '/dashboard',\n    icon: LayoutDashboard,\n  },\n  {\n    name: 'Projects',\n    href: '/dashboard/projects',\n    icon: FolderOpen,\n  },\n  {\n    name: 'Blog Posts',\n    href: '/dashboard/blog',\n    icon: FileText,\n  },\n  {\n    name: 'Services',\n    href: '/dashboard/services',\n    icon: Briefcase,\n  },\n  {\n    name: 'Tech Stack',\n    href: '/dashboard/tech-stack',\n    icon: Code,\n  },\n  {\n    name: 'Testimonials',\n    href: '/dashboard/testimonials',\n    icon: MessageSquare,\n  },\n  {\n    name: 'Analytics',\n    href: '/dashboard/analytics',\n    icon: BarChart3,\n  },\n  {\n    name: 'Settings',\n    href: '/dashboard/settings',\n    icon: Settings,\n  },\n]\n\nexport function DashboardSidebar() {\n  const pathname = usePathname()\n\n  return (\n    <div className=\"flex h-full w-64 flex-col bg-gray-50 dark:bg-gray-900\">\n      <div className=\"flex h-16 items-center px-6\">\n        <h1 className=\"text-xl font-bold\">Portfolio CMS</h1>\n      </div>\n      <nav className=\"flex-1 space-y-1 px-3 py-4\">\n        {navigation.map((item) => {\n          const isActive = pathname === item.href\n          return (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={cn(\n                'flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors',\n                isActive\n                  ? 'bg-primary text-primary-foreground'\n                  : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800'\n              )}\n            >\n              <item.icon className=\"mr-3 h-5 w-5\" />\n              {item.name}\n            </Link>\n          )\n        })}\n      </nav>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAgBA,MAAM,aAAa;IACjB;QACE,MAAM;QACN,MAAM;QACN,MAAM,4NAAA,CAAA,kBAAe;IACvB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,kNAAA,CAAA,aAAU;IAClB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,8MAAA,CAAA,WAAQ;IAChB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,4MAAA,CAAA,YAAS;IACjB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,kMAAA,CAAA,OAAI;IACZ;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,wNAAA,CAAA,gBAAa;IACrB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,kNAAA,CAAA,YAAS;IACjB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,0MAAA,CAAA,WAAQ;IAChB;CACD;AAEM,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;8BAAoB;;;;;;;;;;;0BAEpC,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC;oBACf,MAAM,WAAW,aAAa,KAAK,IAAI;oBACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;wBAEH,MAAM,KAAK,IAAI;wBACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gFACA,WACI,uCACA;;0CAGN,8OAAC,KAAK,IAAI;gCAAC,WAAU;;;;;;4BACpB,KAAK,IAAI;;uBAVL,KAAK,IAAI;;;;;gBAapB;;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/portfolio-cms/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/portfolio-cms/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 490, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/portfolio-cms/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 542, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/portfolio-cms/src/components/dashboard/header.tsx"], "sourcesContent": ["'use client'\n\nimport { signOut, useSession } from 'next-auth/react'\nimport { Button } from '@/components/ui/button'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { Avatar, AvatarFallback } from '@/components/ui/avatar'\nimport { LogOut, User } from 'lucide-react'\n\nexport function DashboardHeader() {\n  const { data: session } = useSession()\n\n  const handleSignOut = () => {\n    signOut({ callbackUrl: '/auth/signin' })\n  }\n\n  return (\n    <header className=\"flex h-16 items-center justify-between border-b bg-white px-6 dark:bg-gray-950\">\n      <div className=\"flex items-center space-x-4\">\n        <h2 className=\"text-lg font-semibold\">Content Management</h2>\n      </div>\n      \n      <div className=\"flex items-center space-x-4\">\n        <DropdownMenu>\n          <DropdownMenuTrigger asChild>\n            <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\n              <Avatar className=\"h-8 w-8\">\n                <AvatarFallback>\n                  {session?.user?.name?.charAt(0) || session?.user?.email?.charAt(0) || 'U'}\n                </AvatarFallback>\n              </Avatar>\n            </Button>\n          </DropdownMenuTrigger>\n          <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n            <DropdownMenuLabel className=\"font-normal\">\n              <div className=\"flex flex-col space-y-1\">\n                <p className=\"text-sm font-medium leading-none\">\n                  {session?.user?.name || 'User'}\n                </p>\n                <p className=\"text-xs leading-none text-muted-foreground\">\n                  {session?.user?.email}\n                </p>\n              </div>\n            </DropdownMenuLabel>\n            <DropdownMenuSeparator />\n            <DropdownMenuItem>\n              <User className=\"mr-2 h-4 w-4\" />\n              <span>Profile</span>\n            </DropdownMenuItem>\n            <DropdownMenuSeparator />\n            <DropdownMenuItem onClick={handleSignOut}>\n              <LogOut className=\"mr-2 h-4 w-4\" />\n              <span>Log out</span>\n            </DropdownMenuItem>\n          </DropdownMenuContent>\n        </DropdownMenu>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAQA;AACA;AAAA;AAbA;;;;;;;AAeO,SAAS;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAEnC,MAAM,gBAAgB;QACpB,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE;YAAE,aAAa;QAAe;IACxC;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;8BAAwB;;;;;;;;;;;0BAGxC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,4IAAA,CAAA,eAAY;;sCACX,8OAAC,4IAAA,CAAA,sBAAmB;4BAAC,OAAO;sCAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,WAAU;0CAChC,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,WAAU;8CAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;kDACZ,SAAS,MAAM,MAAM,OAAO,MAAM,SAAS,MAAM,OAAO,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;sCAK9E,8OAAC,4IAAA,CAAA,sBAAmB;4BAAC,WAAU;4BAAO,OAAM;4BAAM,UAAU;;8CAC1D,8OAAC,4IAAA,CAAA,oBAAiB;oCAAC,WAAU;8CAC3B,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DACV,SAAS,MAAM,QAAQ;;;;;;0DAE1B,8OAAC;gDAAE,WAAU;0DACV,SAAS,MAAM;;;;;;;;;;;;;;;;;8CAItB,8OAAC,4IAAA,CAAA,wBAAqB;;;;;8CACtB,8OAAC,4IAAA,CAAA,mBAAgB;;sDACf,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC,4IAAA,CAAA,wBAAqB;;;;;8CACtB,8OAAC,4IAAA,CAAA,mBAAgB;oCAAC,SAAS;;sDACzB,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB", "debugId": null}}, {"offset": {"line": 739, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/portfolio-cms/src/components/dashboard/layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useSession } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport { useEffect } from 'react'\nimport { DashboardSidebar } from './sidebar'\nimport { DashboardHeader } from './header'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n}\n\nexport function DashboardLayout({ children }: DashboardLayoutProps) {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (status === 'loading') return // Still loading\n\n    if (!session) {\n      router.push('/auth/signin')\n      return\n    }\n  }, [session, status, router])\n\n  if (status === 'loading') {\n    return (\n      <div className=\"flex h-screen items-center justify-center\">\n        <div className=\"text-lg\">Loading...</div>\n      </div>\n    )\n  }\n\n  if (!session) {\n    return null\n  }\n\n  return (\n    <div className=\"flex h-screen bg-gray-100 dark:bg-gray-900\">\n      <DashboardSidebar />\n      <div className=\"flex flex-1 flex-col overflow-hidden\">\n        <DashboardHeader />\n        <main className=\"flex-1 overflow-auto p-6\">\n          {children}\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAYO,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IAChE,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,WAAW,QAAO,gBAAgB;QAEjD,IAAI,CAAC,SAAS;YACZ,OAAO,IAAI,CAAC;YACZ;QACF;IACF,GAAG;QAAC;QAAS;QAAQ;KAAO;IAE5B,IAAI,WAAW,WAAW;QACxB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0IAAA,CAAA,mBAAgB;;;;;0BACjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,yIAAA,CAAA,kBAAe;;;;;kCAChB,8OAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 832, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/portfolio-cms/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 929, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/portfolio-cms/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 975, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/portfolio-cms/src/app/dashboard/analytics/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { DashboardLayout } from '@/components/dashboard/layout'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  BarChart3,\n  <PERSON>,\n  Eye,\n  Clock,\n  TrendingUp,\n  TrendingDown,\n} from 'lucide-react'\n\ninterface AnalyticsData {\n  pageViews: number\n  sessions: number\n  users: number\n  bounceRate: number\n  avgDuration: number\n  topPages: Array<{ page: string; views: number }>\n  topSources: Array<{ source: string; sessions: number }>\n}\n\nexport default function AnalyticsPage() {\n  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    // Simulate analytics data for now\n    setTimeout(() => {\n      setAnalytics({\n        pageViews: 12543,\n        sessions: 8921,\n        users: 6789,\n        bounceRate: 42.3,\n        avgDuration: 185,\n        topPages: [\n          { page: '/', views: 4521 },\n          { page: '/projects', views: 2134 },\n          { page: '/about', views: 1876 },\n          { page: '/blog', views: 1543 },\n          { page: '/contact', views: 987 },\n        ],\n        topSources: [\n          { source: 'Direct', sessions: 3456 },\n          { source: 'Google', sessions: 2890 },\n          { source: 'LinkedIn', sessions: 1234 },\n          { source: 'GitHub', sessions: 987 },\n          { source: 'Twitter', sessions: 354 },\n        ],\n      })\n      setLoading(false)\n    }, 1000)\n  }, [])\n\n  const formatDuration = (seconds: number) => {\n    const minutes = Math.floor(seconds / 60)\n    const remainingSeconds = seconds % 60\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`\n  }\n\n  return (\n    <DashboardLayout>\n      <div className=\"space-y-6\">\n        <div>\n          <h1 className=\"text-3xl font-bold tracking-tight\">Analytics</h1>\n          <p className=\"text-muted-foreground\">\n            Portfolio website performance and visitor insights\n          </p>\n        </div>\n\n        {loading ? (\n          <div className=\"text-center py-8\">Loading analytics...</div>\n        ) : analytics ? (\n          <>\n            {/* Key Metrics */}\n            <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n              <Card>\n                <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                  <CardTitle className=\"text-sm font-medium\">Page Views</CardTitle>\n                  <Eye className=\"h-4 w-4 text-muted-foreground\" />\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-2xl font-bold\">{analytics.pageViews.toLocaleString()}</div>\n                  <p className=\"text-xs text-muted-foreground\">\n                    <TrendingUp className=\"inline h-3 w-3 mr-1\" />\n                    +12.5% from last month\n                  </p>\n                </CardContent>\n              </Card>\n\n              <Card>\n                <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                  <CardTitle className=\"text-sm font-medium\">Sessions</CardTitle>\n                  <BarChart3 className=\"h-4 w-4 text-muted-foreground\" />\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-2xl font-bold\">{analytics.sessions.toLocaleString()}</div>\n                  <p className=\"text-xs text-muted-foreground\">\n                    <TrendingUp className=\"inline h-3 w-3 mr-1\" />\n                    +8.2% from last month\n                  </p>\n                </CardContent>\n              </Card>\n\n              <Card>\n                <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                  <CardTitle className=\"text-sm font-medium\">Users</CardTitle>\n                  <Users className=\"h-4 w-4 text-muted-foreground\" />\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-2xl font-bold\">{analytics.users.toLocaleString()}</div>\n                  <p className=\"text-xs text-muted-foreground\">\n                    <TrendingUp className=\"inline h-3 w-3 mr-1\" />\n                    +15.3% from last month\n                  </p>\n                </CardContent>\n              </Card>\n\n              <Card>\n                <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                  <CardTitle className=\"text-sm font-medium\">Avg. Duration</CardTitle>\n                  <Clock className=\"h-4 w-4 text-muted-foreground\" />\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-2xl font-bold\">{formatDuration(analytics.avgDuration)}</div>\n                  <p className=\"text-xs text-muted-foreground\">\n                    <TrendingDown className=\"inline h-3 w-3 mr-1\" />\n                    -2.1% from last month\n                  </p>\n                </CardContent>\n              </Card>\n            </div>\n\n            {/* Top Pages and Sources */}\n            <div className=\"grid gap-4 md:grid-cols-2\">\n              <Card>\n                <CardHeader>\n                  <CardTitle>Top Pages</CardTitle>\n                  <CardDescription>Most visited pages on your portfolio</CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"space-y-3\">\n                    {analytics.topPages.map((page, index) => (\n                      <div key={page.page} className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center gap-2\">\n                          <Badge variant=\"outline\" className=\"w-6 h-6 p-0 flex items-center justify-center text-xs\">\n                            {index + 1}\n                          </Badge>\n                          <span className=\"font-medium\">{page.page}</span>\n                        </div>\n                        <span className=\"text-sm text-muted-foreground\">\n                          {page.views.toLocaleString()} views\n                        </span>\n                      </div>\n                    ))}\n                  </div>\n                </CardContent>\n              </Card>\n\n              <Card>\n                <CardHeader>\n                  <CardTitle>Traffic Sources</CardTitle>\n                  <CardDescription>Where your visitors are coming from</CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"space-y-3\">\n                    {analytics.topSources.map((source, index) => (\n                      <div key={source.source} className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center gap-2\">\n                          <Badge variant=\"outline\" className=\"w-6 h-6 p-0 flex items-center justify-center text-xs\">\n                            {index + 1}\n                          </Badge>\n                          <span className=\"font-medium\">{source.source}</span>\n                        </div>\n                        <span className=\"text-sm text-muted-foreground\">\n                          {source.sessions.toLocaleString()} sessions\n                        </span>\n                      </div>\n                    ))}\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n\n            {/* Additional Metrics */}\n            <Card>\n              <CardHeader>\n                <CardTitle>Additional Metrics</CardTitle>\n                <CardDescription>More insights about your portfolio performance</CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid gap-4 md:grid-cols-3\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold\">{analytics.bounceRate}%</div>\n                    <p className=\"text-sm text-muted-foreground\">Bounce Rate</p>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold\">\n                      {(analytics.pageViews / analytics.sessions).toFixed(1)}\n                    </div>\n                    <p className=\"text-sm text-muted-foreground\">Pages per Session</p>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold\">\n                      {((analytics.users / analytics.sessions) * 100).toFixed(1)}%\n                    </div>\n                    <p className=\"text-sm text-muted-foreground\">New Visitors</p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </>\n        ) : (\n          <div className=\"text-center py-8\">\n            <p className=\"text-muted-foreground\">No analytics data available</p>\n          </div>\n        )}\n      </div>\n    </DashboardLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAyBe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kCAAkC;QAClC,WAAW;YACT,aAAa;gBACX,WAAW;gBACX,UAAU;gBACV,OAAO;gBACP,YAAY;gBACZ,aAAa;gBACb,UAAU;oBACR;wBAAE,MAAM;wBAAK,OAAO;oBAAK;oBACzB;wBAAE,MAAM;wBAAa,OAAO;oBAAK;oBACjC;wBAAE,MAAM;wBAAU,OAAO;oBAAK;oBAC9B;wBAAE,MAAM;wBAAS,OAAO;oBAAK;oBAC7B;wBAAE,MAAM;wBAAY,OAAO;oBAAI;iBAChC;gBACD,YAAY;oBACV;wBAAE,QAAQ;wBAAU,UAAU;oBAAK;oBACnC;wBAAE,QAAQ;wBAAU,UAAU;oBAAK;oBACnC;wBAAE,QAAQ;wBAAY,UAAU;oBAAK;oBACrC;wBAAE,QAAQ;wBAAU,UAAU;oBAAI;oBAClC;wBAAE,QAAQ;wBAAW,UAAU;oBAAI;iBACpC;YACH;YACA,WAAW;QACb,GAAG;IACL,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;QACrC,MAAM,mBAAmB,UAAU;QACnC,OAAO,GAAG,QAAQ,CAAC,EAAE,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACrE;IAEA,qBACE,8OAAC,yIAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAAoC;;;;;;sCAClD,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;gBAKtC,wBACC,8OAAC;oBAAI,WAAU;8BAAmB;;;;;2BAChC,0BACF;;sCAEE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsB;;;;;;8DAC3C,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;;sDAEjB,8OAAC,gIAAA,CAAA,cAAW;;8DACV,8OAAC;oDAAI,WAAU;8DAAsB,UAAU,SAAS,CAAC,cAAc;;;;;;8DACvE,8OAAC;oDAAE,WAAU;;sEACX,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;wDAAwB;;;;;;;;;;;;;;;;;;;8CAMpD,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsB;;;;;;8DAC3C,8OAAC,kNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;sDAEvB,8OAAC,gIAAA,CAAA,cAAW;;8DACV,8OAAC;oDAAI,WAAU;8DAAsB,UAAU,QAAQ,CAAC,cAAc;;;;;;8DACtE,8OAAC;oDAAE,WAAU;;sEACX,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;wDAAwB;;;;;;;;;;;;;;;;;;;8CAMpD,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsB;;;;;;8DAC3C,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;sDAEnB,8OAAC,gIAAA,CAAA,cAAW;;8DACV,8OAAC;oDAAI,WAAU;8DAAsB,UAAU,KAAK,CAAC,cAAc;;;;;;8DACnE,8OAAC;oDAAE,WAAU;;sEACX,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;wDAAwB;;;;;;;;;;;;;;;;;;;8CAMpD,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsB;;;;;;8DAC3C,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;sDAEnB,8OAAC,gIAAA,CAAA,cAAW;;8DACV,8OAAC;oDAAI,WAAU;8DAAsB,eAAe,UAAU,WAAW;;;;;;8DACzE,8OAAC;oDAAE,WAAU;;sEACX,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;wDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;sCAQxD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;;8DACT,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,gIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAEnB,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;0DACZ,UAAU,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC7B,8OAAC;wDAAoB,WAAU;;0EAC7B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAU,WAAU;kFAChC,QAAQ;;;;;;kFAEX,8OAAC;wEAAK,WAAU;kFAAe,KAAK,IAAI;;;;;;;;;;;;0EAE1C,8OAAC;gEAAK,WAAU;;oEACb,KAAK,KAAK,CAAC,cAAc;oEAAG;;;;;;;;uDARvB,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;8CAgB3B,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;;8DACT,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,gIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAEnB,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;0DACZ,UAAU,UAAU,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACjC,8OAAC;wDAAwB,WAAU;;0EACjC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAU,WAAU;kFAChC,QAAQ;;;;;;kFAEX,8OAAC;wEAAK,WAAU;kFAAe,OAAO,MAAM;;;;;;;;;;;;0EAE9C,8OAAC;gEAAK,WAAU;;oEACb,OAAO,QAAQ,CAAC,cAAc;oEAAG;;;;;;;;uDAR5B,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAkBjC,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAEnB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;4DAAsB,UAAU,UAAU;4DAAC;;;;;;;kEAC1D,8OAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;0DAE/C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,CAAC,UAAU,SAAS,GAAG,UAAU,QAAQ,EAAE,OAAO,CAAC;;;;;;kEAEtD,8OAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;0DAE/C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;4DACZ,CAAC,AAAC,UAAU,KAAK,GAAG,UAAU,QAAQ,GAAI,GAAG,EAAE,OAAO,CAAC;4DAAG;;;;;;;kEAE7D,8OAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iDAOvD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;;;;;AAMjD", "debugId": null}}]}