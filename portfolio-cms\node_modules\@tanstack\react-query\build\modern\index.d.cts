export * from '@tanstack/query-core';
export { AnyUseBaseQueryOptions, AnyUseInfiniteQueryOptions, AnyUseMutationOptions, AnyUseQueryOptions, AnyUseSuspenseInfiniteQueryOptions, AnyUseSuspenseQueryOptions, DefinedUseInfiniteQueryResult, DefinedUseQueryResult, UseBaseMutationResult, UseBaseQueryOptions, UseBaseQueryResult, UseInfiniteQueryOptions, UseInfiniteQueryResult, UseMutateAsyncFunction, UseMutateFunction, UseMutationOptions, UseMutationResult, UsePrefetchQueryOptions, UseQueryOptions, UseQueryResult, UseSuspenseInfiniteQueryOptions, UseSuspenseInfiniteQueryResult, UseSuspenseQueryOptions, UseSuspenseQueryResult } from './types.cjs';
export { QueriesOptions, QueriesResults, useQueries } from './useQueries.cjs';
export { useQuery } from './useQuery.cjs';
export { useSuspenseQuery } from './useSuspenseQuery.cjs';
export { useSuspenseInfiniteQuery } from './useSuspenseInfiniteQuery.cjs';
export { SuspenseQueriesOptions, SuspenseQueriesResults, useSuspenseQueries } from './useSuspenseQueries.cjs';
export { usePrefetchQuery } from './usePrefetchQuery.cjs';
export { usePrefetchInfiniteQuery } from './usePrefetchInfiniteQuery.cjs';
export { DefinedInitialDataOptions, UndefinedInitialDataOptions, UnusedSkipTokenOptions, queryOptions } from './queryOptions.cjs';
export { DefinedInitialDataInfiniteOptions, UndefinedInitialDataInfiniteOptions, UnusedSkipTokenInfiniteOptions, infiniteQueryOptions } from './infiniteQueryOptions.cjs';
export { QueryClientContext, QueryClientProvider, QueryClientProviderProps, useQueryClient } from './QueryClientProvider.cjs';
export { QueryErrorClearResetFunction, QueryErrorIsResetFunction, QueryErrorResetBoundary, QueryErrorResetBoundaryFunction, QueryErrorResetBoundaryProps, QueryErrorResetFunction, useQueryErrorResetBoundary } from './QueryErrorResetBoundary.cjs';
export { HydrationBoundary, HydrationBoundaryProps } from './HydrationBoundary.cjs';
export { useIsFetching } from './useIsFetching.cjs';
export { useIsMutating, useMutationState } from './useMutationState.cjs';
export { useMutation } from './useMutation.cjs';
export { useInfiniteQuery } from './useInfiniteQuery.cjs';
export { IsRestoringProvider, useIsRestoring } from './IsRestoringProvider.cjs';
import 'react';
import 'react/jsx-runtime';
