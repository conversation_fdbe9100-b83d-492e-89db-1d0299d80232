import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosResponseWithHTTP2, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace appengine_v1beta {
    export interface Options extends GlobalOptions {
        version: 'v1beta';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * App Engine Admin API
     *
     * Provisions and manages developers&#39; App Engine applications.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const appengine = google.appengine('v1beta');
     * ```
     */
    export class Appengine {
        context: APIRequestContext;
        apps: Resource$Apps;
        projects: Resource$Projects;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * Google Cloud Endpoints (https://cloud.google.com/endpoints) configuration for API handlers.
     */
    export interface Schema$ApiConfigHandler {
        /**
         * Action to take when users access resources that require authentication. Defaults to redirect.
         */
        authFailAction?: string | null;
        /**
         * Level of login required to access this resource. Defaults to optional.
         */
        login?: string | null;
        /**
         * Path to the script from the application root directory.
         */
        script?: string | null;
        /**
         * Security (HTTPS) enforcement for this URL.
         */
        securityLevel?: string | null;
        /**
         * URL to serve the endpoint at.
         */
        url?: string | null;
    }
    /**
     * Uses Google Cloud Endpoints to handle requests.
     */
    export interface Schema$ApiEndpointHandler {
        /**
         * Path to the script from the application root directory.
         */
        scriptPath?: string | null;
    }
    /**
     * An Application resource contains the top-level configuration of an App Engine application.
     */
    export interface Schema$Application {
        /**
         * Google Apps authentication domain that controls which users can access this application.Defaults to open access for any Google Account.
         */
        authDomain?: string | null;
        /**
         * Output only. Google Cloud Storage bucket that can be used for storing files associated with this application. This bucket is associated with the application and can be used by the gcloud deployment commands.@OutputOnly
         */
        codeBucket?: string | null;
        /**
         * The type of the Cloud Firestore or Cloud Datastore database associated with this application.
         */
        databaseType?: string | null;
        /**
         * Output only. Google Cloud Storage bucket that can be used by this application to store content.@OutputOnly
         */
        defaultBucket?: string | null;
        /**
         * Cookie expiration policy for this application.
         */
        defaultCookieExpiration?: string | null;
        /**
         * Output only. Hostname used to reach this application, as resolved by App Engine.@OutputOnly
         */
        defaultHostname?: string | null;
        /**
         * HTTP path dispatch rules for requests to the application that do not explicitly target a service or version. Rules are order-dependent. Up to 20 dispatch rules can be supported.
         */
        dispatchRules?: Schema$UrlDispatchRule[];
        /**
         * The feature specific settings to be used in the application.
         */
        featureSettings?: Schema$FeatureSettings;
        /**
         * Output only. The Google Container Registry domain used for storing managed build docker images for this application.
         */
        gcrDomain?: string | null;
        /**
         * Additional Google Generated Customer Metadata, this field won't be provided by default and can be requested by setting the IncludeExtraData field in GetApplicationRequest
         */
        generatedCustomerMetadata?: {
            [key: string]: any;
        } | null;
        iap?: Schema$IdentityAwareProxy;
        /**
         * Identifier of the Application resource. This identifier is equivalent to the project ID of the Google Cloud Platform project where you want to deploy your application. Example: myapp.
         */
        id?: string | null;
        /**
         * Location from which this application runs. Application instances run out of the data centers in the specified location, which is also where all of the application's end user content is stored.Defaults to us-central.View the list of supported locations (https://cloud.google.com/appengine/docs/locations).
         */
        locationId?: string | null;
        /**
         * Output only. Full path to the Application resource in the API. Example: apps/myapp.@OutputOnly
         */
        name?: string | null;
        /**
         * The service account associated with the application. This is the app-level default identity. If no identity provided during create version, Admin API will fallback to this one.
         */
        serviceAccount?: string | null;
        /**
         * Serving status of this application.
         */
        servingStatus?: string | null;
        /**
         * The SSL policy that will be applied to the application. If set to Modern it will restrict traffic with TLS < 1.2 and allow only Modern Ciphers suite
         */
        sslPolicy?: string | null;
    }
    /**
     * An SSL certificate that a user has been authorized to administer. A user is authorized to administer any certificate that applies to one of their authorized domains.
     */
    export interface Schema$AuthorizedCertificate {
        /**
         * The SSL certificate serving the AuthorizedCertificate resource. This must be obtained independently from a certificate authority.
         */
        certificateRawData?: Schema$CertificateRawData;
        /**
         * The user-specified display name of the certificate. This is not guaranteed to be unique. Example: My Certificate.
         */
        displayName?: string | null;
        /**
         * Aggregate count of the domain mappings with this certificate mapped. This count includes domain mappings on applications for which the user does not have VIEWER permissions.Only returned by GET or LIST requests when specifically requested by the view=FULL_CERTIFICATE option.@OutputOnly
         */
        domainMappingsCount?: number | null;
        /**
         * Topmost applicable domains of this certificate. This certificate applies to these domains and their subdomains. Example: example.com.@OutputOnly
         */
        domainNames?: string[] | null;
        /**
         * The time when this certificate expires. To update the renewal time on this certificate, upload an SSL certificate with a different expiration time using AuthorizedCertificates.UpdateAuthorizedCertificate.@OutputOnly
         */
        expireTime?: string | null;
        /**
         * Relative name of the certificate. This is a unique value autogenerated on AuthorizedCertificate resource creation. Example: 12345.@OutputOnly
         */
        id?: string | null;
        /**
         * Only applicable if this certificate is managed by App Engine. Managed certificates are tied to the lifecycle of a DomainMapping and cannot be updated or deleted via the AuthorizedCertificates API. If this certificate is manually administered by the user, this field will be empty.@OutputOnly
         */
        managedCertificate?: Schema$ManagedCertificate;
        /**
         * Full path to the AuthorizedCertificate resource in the API. Example: apps/myapp/authorizedCertificates/12345.@OutputOnly
         */
        name?: string | null;
        /**
         * The full paths to user visible Domain Mapping resources that have this certificate mapped. Example: apps/myapp/domainMappings/example.com.This may not represent the full list of mapped domain mappings if the user does not have VIEWER permissions on all of the applications that have this certificate mapped. See domain_mappings_count for a complete count.Only returned by GET or LIST requests when specifically requested by the view=FULL_CERTIFICATE option.@OutputOnly
         */
        visibleDomainMappings?: string[] | null;
    }
    /**
     * A domain that a user has been authorized to administer. To authorize use of a domain, verify ownership via Search Console (https://search.google.com/search-console/welcome).
     */
    export interface Schema$AuthorizedDomain {
        /**
         * Fully qualified domain name of the domain authorized for use. Example: example.com.
         */
        id?: string | null;
        /**
         * Full path to the AuthorizedDomain resource in the API. Example: apps/myapp/authorizedDomains/example.com.@OutputOnly
         */
        name?: string | null;
    }
    /**
     * Automatic scaling is based on request rate, response latencies, and other application metrics.
     */
    export interface Schema$AutomaticScaling {
        /**
         * The time period that the Autoscaler (https://cloud.google.com/compute/docs/autoscaler/) should wait before it starts collecting information from a new instance. This prevents the autoscaler from collecting information when the instance is initializing, during which the collected usage would not be reliable. Only applicable in the App Engine flexible environment.
         */
        coolDownPeriod?: string | null;
        /**
         * Target scaling by CPU usage.
         */
        cpuUtilization?: Schema$CpuUtilization;
        /**
         * Target scaling by user-provided metrics. Only applicable in the App Engine flexible environment.
         */
        customMetrics?: Schema$CustomMetric[];
        /**
         * Target scaling by disk usage.
         */
        diskUtilization?: Schema$DiskUtilization;
        /**
         * Number of concurrent requests an automatic scaling instance can accept before the scheduler spawns a new instance.Defaults to a runtime-specific value.
         */
        maxConcurrentRequests?: number | null;
        /**
         * Maximum number of idle instances that should be maintained for this version.
         */
        maxIdleInstances?: number | null;
        /**
         * Maximum amount of time that a request should wait in the pending queue before starting a new instance to handle it.
         */
        maxPendingLatency?: string | null;
        /**
         * Maximum number of instances that should be started to handle requests for this version.
         */
        maxTotalInstances?: number | null;
        /**
         * Minimum number of idle instances that should be maintained for this version. Only applicable for the default version of a service.
         */
        minIdleInstances?: number | null;
        /**
         * Minimum amount of time a request should wait in the pending queue before starting a new instance to handle it.
         */
        minPendingLatency?: string | null;
        /**
         * Minimum number of running instances that should be maintained for this version.
         */
        minTotalInstances?: number | null;
        /**
         * Target scaling by network usage.
         */
        networkUtilization?: Schema$NetworkUtilization;
        /**
         * Target scaling by request utilization.
         */
        requestUtilization?: Schema$RequestUtilization;
        /**
         * Scheduler settings for standard environment.
         */
        standardSchedulerSettings?: Schema$StandardSchedulerSettings;
    }
    /**
     * A service with basic scaling will create an instance when the application receives a request. The instance will be turned down when the app becomes idle. Basic scaling is ideal for work that is intermittent or driven by user activity.
     */
    export interface Schema$BasicScaling {
        /**
         * Duration of time after the last request that an instance must wait before the instance is shut down.
         */
        idleTimeout?: string | null;
        /**
         * Maximum number of instances to create for this version.
         */
        maxInstances?: number | null;
    }
    /**
     * Request message for Firewall.BatchUpdateIngressRules.
     */
    export interface Schema$BatchUpdateIngressRulesRequest {
        /**
         * A list of FirewallRules to replace the existing set.
         */
        ingressRules?: Schema$FirewallRule[];
    }
    /**
     * Response message for Firewall.UpdateAllIngressRules.
     */
    export interface Schema$BatchUpdateIngressRulesResponse {
        /**
         * The full list of ingress FirewallRules for this application.
         */
        ingressRules?: Schema$FirewallRule[];
    }
    /**
     * Google Cloud Build information.
     */
    export interface Schema$BuildInfo {
        /**
         * The Google Cloud Build id. Example: "f966068f-08b2-42c8-bdfe-74137dff2bf9"
         */
        cloudBuildId?: string | null;
    }
    /**
     * An SSL certificate obtained from a certificate authority.
     */
    export interface Schema$CertificateRawData {
        /**
         * Unencrypted PEM encoded RSA private key. This field is set once on certificate creation and then encrypted. The key size must be 2048 bits or fewer. Must include the header and footer. Example: -----BEGIN RSA PRIVATE KEY----- -----END RSA PRIVATE KEY----- @InputOnly
         */
        privateKey?: string | null;
        /**
         * PEM encoded x.509 public key certificate. This field is set once on certificate creation. Must include the header and footer. Example: -----BEGIN CERTIFICATE----- -----END CERTIFICATE-----
         */
        publicCertificate?: string | null;
    }
    /**
     * Options for the build operations performed as a part of the version deployment. Only applicable for App Engine flexible environment when creating a version using source code directly.
     */
    export interface Schema$CloudBuildOptions {
        /**
         * Path to the yaml file used in deployment, used to determine runtime configuration details.Required for flexible environment builds.See https://cloud.google.com/appengine/docs/standard/python/config/appref for more details.
         */
        appYamlPath?: string | null;
        /**
         * The Cloud Build timeout used as part of any dependent builds performed by version creation. Defaults to 10 minutes.
         */
        cloudBuildTimeout?: string | null;
    }
    /**
     * Docker image that is used to create a container and start a VM instance for the version that you deploy. Only applicable for instances running in the App Engine flexible environment.
     */
    export interface Schema$ContainerInfo {
        /**
         * URI to the hosted container image in Google Container Registry. The URI must be fully qualified and include a tag or digest. Examples: "gcr.io/my-project/image:tag" or "gcr.io/my-project/image@digest"
         */
        image?: string | null;
    }
    /**
     * ContainerState contains the externally-visible container state that is used to communicate the state and reasoning for that state to the CLH. This data is not persisted by CCFE, but is instead derived from CCFE's internal representation of the container state.
     */
    export interface Schema$ContainerState {
        currentReasons?: Schema$Reasons;
        /**
         * The previous and current reasons for a container state will be sent for a container event. CLHs that need to know the signal that caused the container event to trigger (edges) as opposed to just knowing the state can act upon differences in the previous and current reasons.Reasons will be provided for every system: service management, data governance, abuse, and billing.If this is a CCFE-triggered event used for reconciliation then the current reasons will be set to their *_CONTROL_PLANE_SYNC state. The previous reasons will contain the last known set of non-unknown non-control_plane_sync reasons for the state.
         */
        previousReasons?: Schema$Reasons;
        /**
         * The current state of the container. This state is the culmination of all of the opinions from external systems that CCFE knows about of the container.
         */
        state?: string | null;
    }
    /**
     * Target scaling by CPU usage.
     */
    export interface Schema$CpuUtilization {
        /**
         * Period of time over which CPU utilization is calculated.
         */
        aggregationWindowLength?: string | null;
        /**
         * Target CPU utilization ratio to maintain when scaling. Must be between 0 and 1.
         */
        targetUtilization?: number | null;
    }
    /**
     * Metadata for the given google.longrunning.Operation during a google.appengine.v1.CreateVersionRequest.
     */
    export interface Schema$CreateVersionMetadataV1 {
        /**
         * The Cloud Build ID if one was created as part of the version create. @OutputOnly
         */
        cloudBuildId?: string | null;
    }
    /**
     * Metadata for the given google.longrunning.Operation during a google.appengine.v1alpha.CreateVersionRequest.
     */
    export interface Schema$CreateVersionMetadataV1Alpha {
        /**
         * The Cloud Build ID if one was created as part of the version create. @OutputOnly
         */
        cloudBuildId?: string | null;
    }
    /**
     * Metadata for the given google.longrunning.Operation during a google.appengine.v1beta.CreateVersionRequest.
     */
    export interface Schema$CreateVersionMetadataV1Beta {
        /**
         * The Cloud Build ID if one was created as part of the version create. @OutputOnly
         */
        cloudBuildId?: string | null;
    }
    /**
     * Allows autoscaling based on Stackdriver metrics.
     */
    export interface Schema$CustomMetric {
        /**
         * Allows filtering on the metric's fields.
         */
        filter?: string | null;
        /**
         * The name of the metric.
         */
        metricName?: string | null;
        /**
         * May be used instead of target_utilization when an instance can handle a specific amount of work/resources and the metric value is equal to the current amount of work remaining. The autoscaler will try to keep the number of instances equal to the metric value divided by single_instance_assignment.
         */
        singleInstanceAssignment?: number | null;
        /**
         * The type of the metric. Must be a string representing a Stackdriver metric type e.g. GAGUE, DELTA_PER_SECOND, etc.
         */
        targetType?: string | null;
        /**
         * The target value for the metric.
         */
        targetUtilization?: number | null;
    }
    /**
     * Represents a whole or partial calendar date, such as a birthday. The time of day and time zone are either specified elsewhere or are insignificant. The date is relative to the Gregorian Calendar. This can represent one of the following: A full date, with non-zero year, month, and day values. A month and day, with a zero year (for example, an anniversary). A year on its own, with a zero month and a zero day. A year and month, with a zero day (for example, a credit card expiration date).Related types: google.type.TimeOfDay google.type.DateTime google.protobuf.Timestamp
     */
    export interface Schema$Date {
        /**
         * Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.
         */
        day?: number | null;
        /**
         * Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.
         */
        month?: number | null;
        /**
         * Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.
         */
        year?: number | null;
    }
    /**
     * Request message for Instances.DebugInstance.
     */
    export interface Schema$DebugInstanceRequest {
        /**
         * Public SSH key to add to the instance. Examples: [USERNAME]:ssh-rsa [KEY_VALUE] [USERNAME] [USERNAME]:ssh-rsa [KEY_VALUE] google-ssh {"userName":"[USERNAME]","expireOn":"[EXPIRE_TIME]"\}For more information, see Adding and Removing SSH Keys (https://cloud.google.com/compute/docs/instances/adding-removing-ssh-keys).
         */
        sshKey?: string | null;
    }
    /**
     * Code and application artifacts used to deploy a version to App Engine.
     */
    export interface Schema$Deployment {
        /**
         * Google Cloud Build build information. Only applicable for instances running in the App Engine flexible environment.
         */
        build?: Schema$BuildInfo;
        /**
         * Options for any Google Cloud Build builds created as a part of this deployment.These options will only be used if a new build is created, such as when deploying to the App Engine flexible environment using files or zip.
         */
        cloudBuildOptions?: Schema$CloudBuildOptions;
        /**
         * The Docker image for the container that runs the version. Only applicable for instances running in the App Engine flexible environment.
         */
        container?: Schema$ContainerInfo;
        /**
         * Manifest of the files stored in Google Cloud Storage that are included as part of this version. All files must be readable using the credentials supplied with this call.
         */
        files?: {
            [key: string]: Schema$FileInfo;
        } | null;
        /**
         * The zip file for this deployment, if this is a zip deployment.
         */
        zip?: Schema$ZipInfo;
    }
    /**
     * Target scaling by disk usage. Only applicable in the App Engine flexible environment.
     */
    export interface Schema$DiskUtilization {
        /**
         * Target bytes read per second.
         */
        targetReadBytesPerSecond?: number | null;
        /**
         * Target ops read per seconds.
         */
        targetReadOpsPerSecond?: number | null;
        /**
         * Target bytes written per second.
         */
        targetWriteBytesPerSecond?: number | null;
        /**
         * Target ops written per second.
         */
        targetWriteOpsPerSecond?: number | null;
    }
    /**
     * A domain serving an App Engine application.
     */
    export interface Schema$DomainMapping {
        /**
         * Relative name of the domain serving the application. Example: example.com.
         */
        id?: string | null;
        /**
         * Full path to the DomainMapping resource in the API. Example: apps/myapp/domainMapping/example.com.@OutputOnly
         */
        name?: string | null;
        /**
         * The resource records required to configure this domain mapping. These records must be added to the domain's DNS configuration in order to serve the application via this domain mapping.@OutputOnly
         */
        resourceRecords?: Schema$ResourceRecord[];
        /**
         * SSL configuration for this domain. If unconfigured, this domain will not serve with SSL.
         */
        sslSettings?: Schema$SslSettings;
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$Empty {
    }
    /**
     * Google Cloud Endpoints (https://cloud.google.com/endpoints) configuration. The Endpoints API Service provides tooling for serving Open API and gRPC endpoints via an NGINX proxy. Only valid for App Engine Flexible environment deployments.The fields here refer to the name and configuration ID of a "service" resource in the Service Management API (https://cloud.google.com/service-management/overview).
     */
    export interface Schema$EndpointsApiService {
        /**
         * Endpoints service configuration ID as specified by the Service Management API. For example "2016-09-19r1".By default, the rollout strategy for Endpoints is RolloutStrategy.FIXED. This means that Endpoints starts up with a particular configuration ID. When a new configuration is rolled out, Endpoints must be given the new configuration ID. The config_id field is used to give the configuration ID and is required in this case.Endpoints also has a rollout strategy called RolloutStrategy.MANAGED. When using this, Endpoints fetches the latest configuration and does not need the configuration ID. In this case, config_id must be omitted.
         */
        configId?: string | null;
        /**
         * Enable or disable trace sampling. By default, this is set to false for enabled.
         */
        disableTraceSampling?: boolean | null;
        /**
         * Endpoints service name which is the name of the "service" resource in the Service Management API. For example "myapi.endpoints.myproject.cloud.goog"
         */
        name?: string | null;
        /**
         * Endpoints rollout strategy. If FIXED, config_id must be specified. If MANAGED, config_id must be omitted.
         */
        rolloutStrategy?: string | null;
    }
    /**
     * The entrypoint for the application.
     */
    export interface Schema$Entrypoint {
        /**
         * The format should be a shell command that can be fed to bash -c.
         */
        shell?: string | null;
    }
    /**
     * Custom static error page to be served when an error occurs.
     */
    export interface Schema$ErrorHandler {
        /**
         * Error condition this handler applies to.
         */
        errorCode?: string | null;
        /**
         * MIME type of file. Defaults to text/html.
         */
        mimeType?: string | null;
        /**
         * Static file content to be served for this error.
         */
        staticFile?: string | null;
    }
    /**
     * The feature specific settings to be used in the application. These define behaviors that are user configurable.
     */
    export interface Schema$FeatureSettings {
        /**
         * Boolean value indicating if split health checks should be used instead of the legacy health checks. At an app.yaml level, this means defaulting to 'readiness_check' and 'liveness_check' values instead of 'health_check' ones. Once the legacy 'health_check' behavior is deprecated, and this value is always true, this setting can be removed.
         */
        splitHealthChecks?: boolean | null;
        /**
         * If true, use Container-Optimized OS (https://cloud.google.com/container-optimized-os/) base image for VMs, rather than a base Debian image.
         */
        useContainerOptimizedOs?: boolean | null;
    }
    /**
     * Single source file that is part of the version to be deployed. Each source file that is deployed must be specified separately.
     */
    export interface Schema$FileInfo {
        /**
         * The MIME type of the file.Defaults to the value from Google Cloud Storage.
         */
        mimeType?: string | null;
        /**
         * The SHA1 hash of the file, in hex.
         */
        sha1Sum?: string | null;
        /**
         * URL source to use to fetch this file. Must be a URL to a resource in Google Cloud Storage in the form 'http(s)://storage.googleapis.com//'.
         */
        sourceUrl?: string | null;
    }
    /**
     * A single firewall rule that is evaluated against incoming traffic and provides an action to take on matched requests.
     */
    export interface Schema$FirewallRule {
        /**
         * The action to take on matched requests.
         */
        action?: string | null;
        /**
         * An optional string description of this rule. This field has a maximum length of 400 characters.
         */
        description?: string | null;
        /**
         * A positive integer between 1, Int32.MaxValue-1 that defines the order of rule evaluation. Rules with the lowest priority are evaluated first.A default rule at priority Int32.MaxValue matches all IPv4 and IPv6 traffic when no previous rule matches. Only the action of this rule can be modified by the user.
         */
        priority?: number | null;
        /**
         * IP address or range, defined using CIDR notation, of requests that this rule applies to. You can use the wildcard character "*" to match all IPs equivalent to "0/0" and "::/0" together. Examples: *********** or ***********/16 or 2001:db8::/32 or 2001:0db8:0000:0042:0000:8a2e:0370:7334. Truncation will be silently performed on addresses which are not properly truncated. For example, *******/24 is accepted as the same address as *******/24. Similarly, for IPv6, 2001:db8::1/32 is accepted as the same address as 2001:db8::/32.
         */
        sourceRange?: string | null;
    }
    /**
     * Runtime settings for the App Engine flexible environment.
     */
    export interface Schema$FlexibleRuntimeSettings {
        /**
         * The operating system of the application runtime.
         */
        operatingSystem?: string | null;
        /**
         * The runtime version of an App Engine flexible application.
         */
        runtimeVersion?: string | null;
    }
    /**
     * For use only by GCE. GceTag is a wrapper around the GCE administrative tag with parent info.
     */
    export interface Schema$GceTag {
        /**
         * The parents(s) of the tag. Eg. projects/123, folders/456 It usually contains only one parent. But, in some corner cases, it can contain multiple parents. Currently, organizations are not supported.
         */
        parent?: string[] | null;
        /**
         * The administrative_tag name.
         */
        tag?: string | null;
    }
    /**
     * Metadata for the given google.cloud.location.Location.
     */
    export interface Schema$GoogleAppengineV1betaLocationMetadata {
        /**
         * App Engine flexible environment is available in the given location.@OutputOnly
         */
        flexibleEnvironmentAvailable?: boolean | null;
        /**
         * Output only. Search API (https://cloud.google.com/appengine/docs/standard/python/search) is available in the given location.
         */
        searchApiAvailable?: boolean | null;
        /**
         * App Engine standard environment is available in the given location.@OutputOnly
         */
        standardEnvironmentAvailable?: boolean | null;
    }
    /**
     * Health checking configuration for VM instances. Unhealthy instances are killed and replaced with new instances. Only applicable for instances in App Engine flexible environment.
     */
    export interface Schema$HealthCheck {
        /**
         * Interval between health checks.
         */
        checkInterval?: string | null;
        /**
         * Whether to explicitly disable health checks for this instance.
         */
        disableHealthCheck?: boolean | null;
        /**
         * Number of consecutive successful health checks required before receiving traffic.
         */
        healthyThreshold?: number | null;
        /**
         * Host header to send when performing an HTTP health check. Example: "myapp.appspot.com"
         */
        host?: string | null;
        /**
         * Number of consecutive failed health checks required before an instance is restarted.
         */
        restartThreshold?: number | null;
        /**
         * Time before the health check is considered failed.
         */
        timeout?: string | null;
        /**
         * Number of consecutive failed health checks required before removing traffic.
         */
        unhealthyThreshold?: number | null;
    }
    /**
     * Identity-Aware Proxy
     */
    export interface Schema$IdentityAwareProxy {
        /**
         * Whether the serving infrastructure will authenticate and authorize all incoming requests.If true, the oauth2_client_id and oauth2_client_secret fields must be non-empty.
         */
        enabled?: boolean | null;
        /**
         * OAuth2 client ID to use for the authentication flow.
         */
        oauth2ClientId?: string | null;
        /**
         * OAuth2 client secret to use for the authentication flow.For security reasons, this value cannot be retrieved via the API. Instead, the SHA-256 hash of the value is returned in the oauth2_client_secret_sha256 field.@InputOnly
         */
        oauth2ClientSecret?: string | null;
        /**
         * Output only. Hex-encoded SHA-256 hash of the client secret.@OutputOnly
         */
        oauth2ClientSecretSha256?: string | null;
    }
    /**
     * An Instance resource is the computing unit that App Engine uses to automatically scale an application.
     */
    export interface Schema$Instance {
        /**
         * Output only. App Engine release this instance is running on.
         */
        appEngineRelease?: string | null;
        /**
         * Output only. Availability of the instance.
         */
        availability?: string | null;
        /**
         * Output only. Average latency (ms) over the last minute.
         */
        averageLatency?: number | null;
        /**
         * Output only. Number of errors since this instance was started.
         */
        errors?: number | null;
        /**
         * Output only. Relative name of the instance within the version. Example: instance-1.
         */
        id?: string | null;
        /**
         * Output only. Total memory in use (bytes).
         */
        memoryUsage?: string | null;
        /**
         * Output only. Full path to the Instance resource in the API. Example: apps/myapp/services/default/versions/v1/instances/instance-1.
         */
        name?: string | null;
        /**
         * Output only. Average queries per second (QPS) over the last minute.
         */
        qps?: number | null;
        /**
         * Output only. Number of requests since this instance was started.
         */
        requests?: number | null;
        /**
         * Output only. Time that this instance was started.@OutputOnly
         */
        startTime?: string | null;
        /**
         * Output only. Whether this instance is in debug mode. Only applicable for instances in App Engine flexible environment.
         */
        vmDebugEnabled?: boolean | null;
        /**
         * Output only. Virtual machine ID of this instance. Only applicable for instances in App Engine flexible environment.
         */
        vmId?: string | null;
        /**
         * Output only. The IP address of this instance. Only applicable for instances in App Engine flexible environment.
         */
        vmIp?: string | null;
        /**
         * Output only. The liveness health check of this instance. Only applicable for instances in App Engine flexible environment.
         */
        vmLiveness?: string | null;
        /**
         * Output only. Name of the virtual machine where this instance lives. Only applicable for instances in App Engine flexible environment.
         */
        vmName?: string | null;
        /**
         * Output only. Status of the virtual machine where this instance lives. Only applicable for instances in App Engine flexible environment.
         */
        vmStatus?: string | null;
        /**
         * Output only. Zone where the virtual machine is located. Only applicable for instances in App Engine flexible environment.
         */
        vmZoneName?: string | null;
    }
    /**
     * Third-party Python runtime library that is required by the application.
     */
    export interface Schema$Library {
        /**
         * Name of the library. Example: "django".
         */
        name?: string | null;
        /**
         * Version of the library to select, or "latest".
         */
        version?: string | null;
    }
    /**
     * Response message for AuthorizedCertificates.ListAuthorizedCertificates.
     */
    export interface Schema$ListAuthorizedCertificatesResponse {
        /**
         * The SSL certificates the user is authorized to administer.
         */
        certificates?: Schema$AuthorizedCertificate[];
        /**
         * Continuation token for fetching the next page of results.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response message for AuthorizedDomains.ListAuthorizedDomains.
     */
    export interface Schema$ListAuthorizedDomainsResponse {
        /**
         * The authorized domains belonging to the user.
         */
        domains?: Schema$AuthorizedDomain[];
        /**
         * Continuation token for fetching the next page of results.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response message for DomainMappings.ListDomainMappings.
     */
    export interface Schema$ListDomainMappingsResponse {
        /**
         * The domain mappings for the application.
         */
        domainMappings?: Schema$DomainMapping[];
        /**
         * Continuation token for fetching the next page of results.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response message for Firewall.ListIngressRules.
     */
    export interface Schema$ListIngressRulesResponse {
        /**
         * The ingress FirewallRules for this application.
         */
        ingressRules?: Schema$FirewallRule[];
        /**
         * Continuation token for fetching the next page of results.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response message for Instances.ListInstances.
     */
    export interface Schema$ListInstancesResponse {
        /**
         * The instances belonging to the requested version.
         */
        instances?: Schema$Instance[];
        /**
         * Continuation token for fetching the next page of results.
         */
        nextPageToken?: string | null;
    }
    /**
     * The response message for Locations.ListLocations.
     */
    export interface Schema$ListLocationsResponse {
        /**
         * A list of locations that matches the specified filter in the request.
         */
        locations?: Schema$Location[];
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
    }
    /**
     * The response message for Operations.ListOperations.
     */
    export interface Schema$ListOperationsResponse {
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
        /**
         * A list of operations that matches the specified filter in the request.
         */
        operations?: Schema$Operation[];
    }
    /**
     * Response message for Applications.ListRuntimes.
     */
    export interface Schema$ListRuntimesResponse {
        /**
         * Continuation token for fetching the next page of results.
         */
        nextPageToken?: string | null;
        /**
         * The runtimes available to the requested application.
         */
        runtimes?: Schema$Runtime[];
    }
    /**
     * Response message for Services.ListServices.
     */
    export interface Schema$ListServicesResponse {
        /**
         * Continuation token for fetching the next page of results.
         */
        nextPageToken?: string | null;
        /**
         * The services belonging to the requested application.
         */
        services?: Schema$Service[];
    }
    /**
     * Response message for Versions.ListVersions.
     */
    export interface Schema$ListVersionsResponse {
        /**
         * Continuation token for fetching the next page of results.
         */
        nextPageToken?: string | null;
        /**
         * The versions belonging to the requested service.
         */
        versions?: Schema$Version[];
    }
    /**
     * Health checking configuration for VM instances. Unhealthy instances are killed and replaced with new instances.
     */
    export interface Schema$LivenessCheck {
        /**
         * Interval between health checks.
         */
        checkInterval?: string | null;
        /**
         * Number of consecutive failed checks required before considering the VM unhealthy.
         */
        failureThreshold?: number | null;
        /**
         * Host header to send when performing a HTTP Liveness check. Example: "myapp.appspot.com"
         */
        host?: string | null;
        /**
         * The initial delay before starting to execute the checks.
         */
        initialDelay?: string | null;
        /**
         * The request path.
         */
        path?: string | null;
        /**
         * Number of consecutive successful checks required before considering the VM healthy.
         */
        successThreshold?: number | null;
        /**
         * Time before the check is considered failed.
         */
        timeout?: string | null;
    }
    /**
     * A resource that represents a Google Cloud location.
     */
    export interface Schema$Location {
        /**
         * The friendly name for this location, typically a nearby city name. For example, "Tokyo".
         */
        displayName?: string | null;
        /**
         * Cross-service attributes for the location. For example {"cloud.googleapis.com/region": "us-east1"\}
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * The canonical id for this location. For example: "us-east1".
         */
        locationId?: string | null;
        /**
         * Service-specific metadata. For example the available capacity at the given location.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * Resource name for the location, which may vary between implementations. For example: "projects/example-project/locations/us-east1"
         */
        name?: string | null;
    }
    /**
     * Metadata for the given google.cloud.location.Location.
     */
    export interface Schema$LocationMetadata {
        /**
         * App Engine flexible environment is available in the given location.@OutputOnly
         */
        flexibleEnvironmentAvailable?: boolean | null;
        /**
         * Output only. Search API (https://cloud.google.com/appengine/docs/standard/python/search) is available in the given location.
         */
        searchApiAvailable?: boolean | null;
        /**
         * App Engine standard environment is available in the given location.@OutputOnly
         */
        standardEnvironmentAvailable?: boolean | null;
    }
    /**
     * A certificate managed by App Engine.
     */
    export interface Schema$ManagedCertificate {
        /**
         * Time at which the certificate was last renewed. The renewal process is fully managed. Certificate renewal will automatically occur before the certificate expires. Renewal errors can be tracked via ManagementStatus.@OutputOnly
         */
        lastRenewalTime?: string | null;
        /**
         * Status of certificate management. Refers to the most recent certificate acquisition or renewal attempt.@OutputOnly
         */
        status?: string | null;
    }
    /**
     * A service with manual scaling runs continuously, allowing you to perform complex initialization and rely on the state of its memory over time.
     */
    export interface Schema$ManualScaling {
        /**
         * Number of instances to assign to the service at the start. This number can later be altered by using the Modules API (https://cloud.google.com/appengine/docs/python/modules/functions) set_num_instances() function.
         */
        instances?: number | null;
    }
    /**
     * Extra network settings. Only applicable in the App Engine flexible environment.
     */
    export interface Schema$Network {
        /**
         * List of ports, or port pairs, to forward from the virtual machine to the application container. Only applicable in the App Engine flexible environment.
         */
        forwardedPorts?: string[] | null;
        /**
         * The IP mode for instances. Only applicable in the App Engine flexible environment.
         */
        instanceIpMode?: string | null;
        /**
         * Tag to apply to the instance during creation. Only applicable in the App Engine flexible environment.
         */
        instanceTag?: string | null;
        /**
         * Google Compute Engine network where the virtual machines are created. Specify the short name, not the resource path.Defaults to default.
         */
        name?: string | null;
        /**
         * Enable session affinity. Only applicable in the App Engine flexible environment.
         */
        sessionAffinity?: boolean | null;
        /**
         * Google Cloud Platform sub-network where the virtual machines are created. Specify the short name, not the resource path.If a subnetwork name is specified, a network name will also be required unless it is for the default network. If the network that the instance is being created in is a Legacy network, then the IP address is allocated from the IPv4Range. If the network that the instance is being created in is an auto Subnet Mode Network, then only network name should be specified (not the subnetwork_name) and the IP address is created from the IPCidrRange of the subnetwork that exists in that zone for that network. If the network that the instance is being created in is a custom Subnet Mode Network, then the subnetwork_name must be specified and the IP address is created from the IPCidrRange of the subnetwork.If specified, the subnetwork must exist in the same region as the App Engine flexible environment application.
         */
        subnetworkName?: string | null;
    }
    /**
     * A NetworkSettings resource is a container for ingress settings for a version or service.
     */
    export interface Schema$NetworkSettings {
        /**
         * The ingress settings for version or service.
         */
        ingressTrafficAllowed?: string | null;
    }
    /**
     * Target scaling by network usage. Only applicable in the App Engine flexible environment.
     */
    export interface Schema$NetworkUtilization {
        /**
         * Target bytes received per second.
         */
        targetReceivedBytesPerSecond?: number | null;
        /**
         * Target packets received per second.
         */
        targetReceivedPacketsPerSecond?: number | null;
        /**
         * Target bytes sent per second.
         */
        targetSentBytesPerSecond?: number | null;
        /**
         * Target packets sent per second.
         */
        targetSentPacketsPerSecond?: number | null;
    }
    /**
     * This resource represents a long-running operation that is the result of a network API call.
     */
    export interface Schema$Operation {
        /**
         * If the value is false, it means the operation is still in progress. If true, the operation is completed, and either error or response is available.
         */
        done?: boolean | null;
        /**
         * The error result of the operation in case of failure or cancellation.
         */
        error?: Schema$Status;
        /**
         * Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the name should be a resource name ending with operations/{unique_id\}.
         */
        name?: string | null;
        /**
         * The normal, successful response of the operation. If the original method returns no data on success, such as Delete, the response is google.protobuf.Empty. If the original method is standard Get/Create/Update, the response should be the resource. For other methods, the response should have the type XxxResponse, where Xxx is the original method name. For example, if the original method name is TakeSnapshot(), the inferred response type is TakeSnapshotResponse.
         */
        response?: {
            [key: string]: any;
        } | null;
    }
    /**
     * Metadata for the given google.longrunning.Operation.
     */
    export interface Schema$OperationMetadataV1 {
        createVersionMetadata?: Schema$CreateVersionMetadataV1;
        /**
         * Time that this operation completed.@OutputOnly
         */
        endTime?: string | null;
        /**
         * Ephemeral message that may change every time the operation is polled. @OutputOnly
         */
        ephemeralMessage?: string | null;
        /**
         * Time that this operation was created.@OutputOnly
         */
        insertTime?: string | null;
        /**
         * API method that initiated this operation. Example: google.appengine.v1.Versions.CreateVersion.@OutputOnly
         */
        method?: string | null;
        /**
         * Name of the resource that this operation is acting on. Example: apps/myapp/services/default.@OutputOnly
         */
        target?: string | null;
        /**
         * User who requested this operation.@OutputOnly
         */
        user?: string | null;
        /**
         * Durable messages that persist on every operation poll. @OutputOnly
         */
        warning?: string[] | null;
    }
    /**
     * Metadata for the given google.longrunning.Operation.
     */
    export interface Schema$OperationMetadataV1Alpha {
        createVersionMetadata?: Schema$CreateVersionMetadataV1Alpha;
        /**
         * Time that this operation completed.@OutputOnly
         */
        endTime?: string | null;
        /**
         * Ephemeral message that may change every time the operation is polled. @OutputOnly
         */
        ephemeralMessage?: string | null;
        /**
         * Time that this operation was created.@OutputOnly
         */
        insertTime?: string | null;
        /**
         * API method that initiated this operation. Example: google.appengine.v1alpha.Versions.CreateVersion.@OutputOnly
         */
        method?: string | null;
        /**
         * Name of the resource that this operation is acting on. Example: apps/myapp/services/default.@OutputOnly
         */
        target?: string | null;
        /**
         * User who requested this operation.@OutputOnly
         */
        user?: string | null;
        /**
         * Durable messages that persist on every operation poll. @OutputOnly
         */
        warning?: string[] | null;
    }
    /**
     * Metadata for the given google.longrunning.Operation.
     */
    export interface Schema$OperationMetadataV1Beta {
        createVersionMetadata?: Schema$CreateVersionMetadataV1Beta;
        /**
         * Time that this operation completed.@OutputOnly
         */
        endTime?: string | null;
        /**
         * Ephemeral message that may change every time the operation is polled. @OutputOnly
         */
        ephemeralMessage?: string | null;
        /**
         * Time that this operation was created.@OutputOnly
         */
        insertTime?: string | null;
        /**
         * API method that initiated this operation. Example: google.appengine.v1beta.Versions.CreateVersion.@OutputOnly
         */
        method?: string | null;
        /**
         * Name of the resource that this operation is acting on. Example: apps/myapp/services/default.@OutputOnly
         */
        target?: string | null;
        /**
         * User who requested this operation.@OutputOnly
         */
        user?: string | null;
        /**
         * Durable messages that persist on every operation poll. @OutputOnly
         */
        warning?: string[] | null;
    }
    /**
     * The request sent to CLHs during project events.
     */
    export interface Schema$ProjectEvent {
        /**
         * The unique ID for this project event. CLHs can use this value to dedup repeated calls. required
         */
        eventId?: string | null;
        /**
         * Phase indicates when in the container event propagation this event is being communicated. Events are sent before and after the per-resource events are propagated. required
         */
        phase?: string | null;
        /**
         * The projects metadata for this project. required
         */
        projectMetadata?: Schema$ProjectsMetadata;
        /**
         * The state of the organization that led to this event.
         */
        state?: Schema$ContainerState;
    }
    /**
     * ProjectsMetadata is the metadata CCFE stores about the all the relevant projects (tenant, consumer, producer).
     */
    export interface Schema$ProjectsMetadata {
        /**
         * The consumer project id.
         */
        consumerProjectId?: string | null;
        /**
         * The consumer project number.
         */
        consumerProjectNumber?: string | null;
        /**
         * The CCFE state of the consumer project. It is the same state that is communicated to the CLH during project events. Notice that this field is not set in the DB, it is only set in this proto when communicated to CLH in the side channel.
         */
        consumerProjectState?: string | null;
        /**
         * The GCE tags associated with the consumer project and those inherited due to their ancestry, if any. Not supported by CCFE.
         */
        gceTag?: Schema$GceTag[];
        /**
         * The service account authorized to operate on the consumer project. Note: CCFE only propagates P4SA with default tag to CLH.
         */
        p4ServiceAccount?: string | null;
        /**
         * The producer project id.
         */
        producerProjectId?: string | null;
        /**
         * The producer project number.
         */
        producerProjectNumber?: string | null;
        /**
         * The tenant project id.
         */
        tenantProjectId?: string | null;
        /**
         * The tenant project number.
         */
        tenantProjectNumber?: string | null;
    }
    /**
     * Readiness checking configuration for VM instances. Unhealthy instances are removed from traffic rotation.
     */
    export interface Schema$ReadinessCheck {
        /**
         * A maximum time limit on application initialization, measured from moment the application successfully replies to a healthcheck until it is ready to serve traffic.
         */
        appStartTimeout?: string | null;
        /**
         * Interval between health checks.
         */
        checkInterval?: string | null;
        /**
         * Number of consecutive failed checks required before removing traffic.
         */
        failureThreshold?: number | null;
        /**
         * Host header to send when performing a HTTP Readiness check. Example: "myapp.appspot.com"
         */
        host?: string | null;
        /**
         * The request path.
         */
        path?: string | null;
        /**
         * Number of consecutive successful checks required before receiving traffic.
         */
        successThreshold?: number | null;
        /**
         * Time before the check is considered failed.
         */
        timeout?: string | null;
    }
    /**
     * Containers transition between and within states based on reasons sent from various systems. CCFE will provide the CLH with reasons for the current state per system.The current systems that CCFE supports are: Service Management (Inception) Data Governance (Wipeout) Abuse (Ares) Billing (Internal Cloud Billing API) Service Activation (Service Controller)
     */
    export interface Schema$Reasons {
        abuse?: string | null;
        billing?: string | null;
        dataGovernance?: string | null;
        /**
         * Consumer Container denotes if the service is active within a project or not. This information could be used to clean up resources in case service in DISABLED_FULL i.e. Service is inactive \> 30 days.
         */
        serviceActivation?: string | null;
        serviceManagement?: string | null;
    }
    /**
     * Request message for 'Applications.RepairApplication'.
     */
    export interface Schema$RepairApplicationRequest {
    }
    /**
     * Target scaling by request utilization. Only applicable in the App Engine flexible environment.
     */
    export interface Schema$RequestUtilization {
        /**
         * Target number of concurrent requests.
         */
        targetConcurrentRequests?: number | null;
        /**
         * Target requests per second.
         */
        targetRequestCountPerSecond?: number | null;
    }
    /**
     * The request that is passed to CLH during per-resource events. The request will be sent with update semantics in all cases except for data governance purge events. These events will be sent with delete semantics and the CLH is expected to delete the resource receiving this event.
     */
    export interface Schema$ResourceEvent {
        /**
         * The unique ID for this per-resource event. CLHs can use this value to dedup repeated calls. required
         */
        eventId?: string | null;
        /**
         * The name of the resource for which this event is. required
         */
        name?: string | null;
        /**
         * The state of the project that led to this event.
         */
        state?: Schema$ContainerState;
    }
    /**
     * A DNS resource record.
     */
    export interface Schema$ResourceRecord {
        /**
         * Relative name of the object affected by this record. Only applicable for CNAME records. Example: 'www'.
         */
        name?: string | null;
        /**
         * Data for this record. Values vary by record type, as defined in RFC 1035 (section 5) and RFC 1034 (section 3.6.1).
         */
        rrdata?: string | null;
        /**
         * Resource record type. Example: AAAA.
         */
        type?: string | null;
    }
    /**
     * Machine resources for a version.
     */
    export interface Schema$Resources {
        /**
         * Number of CPU cores needed.
         */
        cpu?: number | null;
        /**
         * Disk size (GB) needed.
         */
        diskGb?: number | null;
        /**
         * The name of the encryption key that is stored in Google Cloud KMS. Only should be used by Cloud Composer to encrypt the vm disk
         */
        kmsKeyReference?: string | null;
        /**
         * Memory (GB) needed.
         */
        memoryGb?: number | null;
        /**
         * User specified volumes.
         */
        volumes?: Schema$Volume[];
    }
    /**
     * Runtime versions for App Engine.
     */
    export interface Schema$Runtime {
        /**
         * Date when Runtime is decommissioned.
         */
        decommissionedDate?: Schema$Date;
        /**
         * Date when Runtime is deprecated.
         */
        deprecationDate?: Schema$Date;
        /**
         * User-friendly display name, e.g. 'Node.js 12', etc.
         */
        displayName?: string | null;
        /**
         * Date when Runtime is end of support.
         */
        endOfSupportDate?: Schema$Date;
        /**
         * The environment of the runtime.
         */
        environment?: string | null;
        /**
         * The name of the runtime, e.g., 'go113', 'nodejs12', etc.
         */
        name?: string | null;
        /**
         * The stage of life this runtime is in, e.g., BETA, GA, etc.
         */
        stage?: string | null;
        /**
         * Supported operating systems for the runtime, e.g., 'ubuntu22', etc.
         */
        supportedOperatingSystems?: string[] | null;
        /**
         * Warning messages, e.g., a deprecation warning.
         */
        warnings?: string[] | null;
    }
    /**
     * Executes a script to handle the request that matches the URL pattern.
     */
    export interface Schema$ScriptHandler {
        /**
         * Path to the script from the application root directory.
         */
        scriptPath?: string | null;
    }
    /**
     * A Service resource is a logical component of an application that can share state and communicate in a secure fashion with other services. For example, an application that handles customer requests might include separate services to handle tasks such as backend data analysis or API requests from mobile devices. Each service has a collection of versions that define a specific set of code used to implement the functionality of that service.
     */
    export interface Schema$Service {
        /**
         * Additional Google Generated Customer Metadata, this field won't be provided by default and can be requested by setting the IncludeExtraData field in GetServiceRequest
         */
        generatedCustomerMetadata?: {
            [key: string]: any;
        } | null;
        /**
         * Output only. Relative name of the service within the application. Example: default.@OutputOnly
         */
        id?: string | null;
        /**
         * A set of labels to apply to this service. Labels are key/value pairs that describe the service and all resources that belong to it (e.g., versions). The labels can be used to search and group resources, and are propagated to the usage and billing reports, enabling fine-grain analysis of costs. An example of using labels is to tag resources belonging to different environments (e.g., "env=prod", "env=qa"). Label keys and values can be no longer than 63 characters and can only contain lowercase letters, numeric characters, underscores, dashes, and international characters. Label keys must start with a lowercase letter or an international character. Each service can have at most 32 labels.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. Full path to the Service resource in the API. Example: apps/myapp/services/default.@OutputOnly
         */
        name?: string | null;
        /**
         * Ingress settings for this service. Will apply to all versions.
         */
        networkSettings?: Schema$NetworkSettings;
        /**
         * Mapping that defines fractional HTTP traffic diversion to different versions within the service.
         */
        split?: Schema$TrafficSplit;
    }
    /**
     * SSL configuration for a DomainMapping resource.
     */
    export interface Schema$SslSettings {
        /**
         * ID of the AuthorizedCertificate resource configuring SSL for the application. Clearing this field will remove SSL support.By default, a managed certificate is automatically created for every domain mapping. To omit SSL support or to configure SSL manually, specify SslManagementType.MANUAL on a CREATE or UPDATE request. You must be authorized to administer the AuthorizedCertificate resource to manually map it to a DomainMapping resource. Example: 12345.
         */
        certificateId?: string | null;
        /**
         * ID of the managed AuthorizedCertificate resource currently being provisioned, if applicable. Until the new managed certificate has been successfully provisioned, the previous SSL state will be preserved. Once the provisioning process completes, the certificate_id field will reflect the new managed certificate and this field will be left empty. To remove SSL support while there is still a pending managed certificate, clear the certificate_id field with an UpdateDomainMappingRequest.@OutputOnly
         */
        pendingManagedCertificateId?: string | null;
        /**
         * SSL management type for this domain. If AUTOMATIC, a managed certificate is automatically provisioned. If MANUAL, certificate_id must be manually specified in order to configure SSL for this domain.
         */
        sslManagementType?: string | null;
    }
    /**
     * Scheduler settings for standard environment.
     */
    export interface Schema$StandardSchedulerSettings {
        /**
         * Maximum number of instances to run for this version. Set to 2147483647 to disable max_instances configuration.
         */
        maxInstances?: number | null;
        /**
         * Minimum number of instances to run for this version. Set to zero to disable min_instances configuration.
         */
        minInstances?: number | null;
        /**
         * Target CPU utilization ratio to maintain when scaling.
         */
        targetCpuUtilization?: number | null;
        /**
         * Target throughput utilization ratio to maintain when scaling
         */
        targetThroughputUtilization?: number | null;
    }
    /**
     * Files served directly to the user for a given URL, such as images, CSS stylesheets, or JavaScript source files. Static file handlers describe which files in the application directory are static files, and which URLs serve them.
     */
    export interface Schema$StaticFilesHandler {
        /**
         * Whether files should also be uploaded as code data. By default, files declared in static file handlers are uploaded as static data and are only served to end users; they cannot be read by the application. If enabled, uploads are charged against both your code and static data storage resource quotas.
         */
        applicationReadable?: boolean | null;
        /**
         * Time a static file served by this handler should be cached by web proxies and browsers.
         */
        expiration?: string | null;
        /**
         * HTTP headers to use for all responses from these URLs.
         */
        httpHeaders?: {
            [key: string]: string;
        } | null;
        /**
         * MIME type used to serve all files served by this handler.Defaults to file-specific MIME types, which are derived from each file's filename extension.
         */
        mimeType?: string | null;
        /**
         * Path to the static files matched by the URL pattern, from the application root directory. The path can refer to text matched in groupings in the URL pattern.
         */
        path?: string | null;
        /**
         * Whether this handler should match the request if the file referenced by the handler does not exist.
         */
        requireMatchingFile?: boolean | null;
        /**
         * Regular expression that matches the file paths for all files that should be referenced by this handler.
         */
        uploadPathRegex?: string | null;
    }
    /**
     * The Status type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by gRPC (https://github.com/grpc). Each Status message contains three pieces of data: error code, error message, and error details.You can find out more about this error model and how to work with it in the API Design Guide (https://cloud.google.com/apis/design/errors).
     */
    export interface Schema$Status {
        /**
         * The status code, which should be an enum value of google.rpc.Code.
         */
        code?: number | null;
        /**
         * A list of messages that carry the error details. There is a common set of message types for APIs to use.
         */
        details?: Array<{
            [key: string]: any;
        }> | null;
        /**
         * A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.
         */
        message?: string | null;
    }
    /**
     * Traffic routing configuration for versions within a single service. Traffic splits define how traffic directed to the service is assigned to versions.
     */
    export interface Schema$TrafficSplit {
        /**
         * Mapping from version IDs within the service to fractional (0.000, 1] allocations of traffic for that version. Each version can be specified only once, but some versions in the service may not have any traffic allocation. Services that have traffic allocated cannot be deleted until either the service is deleted or their traffic allocation is removed. Allocations must sum to 1. Up to two decimal place precision is supported for IP-based splits and up to three decimal places is supported for cookie-based splits.
         */
        allocations?: {
            [key: string]: number;
        } | null;
        /**
         * Mechanism used to determine which version a request is sent to. The traffic selection algorithm will be stable for either type until allocations are changed.
         */
        shardBy?: string | null;
    }
    /**
     * Rules to match an HTTP request and dispatch that request to a service.
     */
    export interface Schema$UrlDispatchRule {
        /**
         * Domain name to match against. The wildcard "*" is supported if specified before a period: "*.".Defaults to matching all domains: "*".
         */
        domain?: string | null;
        /**
         * Pathname within the host. Must start with a "/". A single "*" can be included at the end of the path.The sum of the lengths of the domain and path may not exceed 100 characters.
         */
        path?: string | null;
        /**
         * Resource ID of a service in this application that should serve the matched request. The service must already exist. Example: default.
         */
        service?: string | null;
    }
    /**
     * URL pattern and description of how the URL should be handled. App Engine can handle URLs by executing application code or by serving static files uploaded with the version, such as images, CSS, or JavaScript.
     */
    export interface Schema$UrlMap {
        /**
         * Uses API Endpoints to handle requests.
         */
        apiEndpoint?: Schema$ApiEndpointHandler;
        /**
         * Action to take when users access resources that require authentication. Defaults to redirect.
         */
        authFailAction?: string | null;
        /**
         * Level of login required to access this resource. Not supported for Node.js in the App Engine standard environment.
         */
        login?: string | null;
        /**
         * 30x code to use when performing redirects for the secure field. Defaults to 302.
         */
        redirectHttpResponseCode?: string | null;
        /**
         * Executes a script to handle the requests that match this URL pattern. Only the auto value is supported for Node.js in the App Engine standard environment, for example "script": "auto".
         */
        script?: Schema$ScriptHandler;
        /**
         * Security (HTTPS) enforcement for this URL.
         */
        securityLevel?: string | null;
        /**
         * Returns the contents of a file, such as an image, as the response.
         */
        staticFiles?: Schema$StaticFilesHandler;
        /**
         * URL prefix. Uses regular expression syntax, which means regexp special characters must be escaped, but should not contain groupings. All URLs that begin with this prefix are handled by this handler, using the portion of the URL after the prefix as part of the file path.
         */
        urlRegex?: string | null;
    }
    /**
     * A Version resource is a specific set of source code and configuration files that are deployed into a service.
     */
    export interface Schema$Version {
        /**
         * Serving configuration for Google Cloud Endpoints (https://cloud.google.com/endpoints).Only returned in GET requests if view=FULL is set.
         */
        apiConfig?: Schema$ApiConfigHandler;
        /**
         * Allows App Engine second generation runtimes to access the legacy bundled services.
         */
        appEngineApis?: boolean | null;
        /**
         * Automatic scaling is based on request rate, response latencies, and other application metrics. Instances are dynamically created and destroyed as needed in order to handle traffic.
         */
        automaticScaling?: Schema$AutomaticScaling;
        /**
         * A service with basic scaling will create an instance when the application receives a request. The instance will be turned down when the app becomes idle. Basic scaling is ideal for work that is intermittent or driven by user activity.
         */
        basicScaling?: Schema$BasicScaling;
        /**
         * Metadata settings that are supplied to this version to enable beta runtime features.
         */
        betaSettings?: {
            [key: string]: string;
        } | null;
        /**
         * Environment variables available to the build environment.Only returned in GET requests if view=FULL is set.
         */
        buildEnvVariables?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. Email address of the user who created this version.@OutputOnly
         */
        createdBy?: string | null;
        /**
         * Time that this version was created.@OutputOnly
         */
        createTime?: string | null;
        /**
         * Duration that static files should be cached by web proxies and browsers. Only applicable if the corresponding StaticFilesHandler (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#StaticFilesHandler) does not specify its own expiration time.Only returned in GET requests if view=FULL is set.
         */
        defaultExpiration?: string | null;
        /**
         * Code and application artifacts that make up this version.Only returned in GET requests if view=FULL is set.
         */
        deployment?: Schema$Deployment;
        /**
         * Output only. Total size in bytes of all the files that are included in this version and currently hosted on the App Engine disk.@OutputOnly
         */
        diskUsageBytes?: string | null;
        /**
         * Cloud Endpoints configuration.If endpoints_api_service is set, the Cloud Endpoints Extensible Service Proxy will be provided to serve the API implemented by the app.
         */
        endpointsApiService?: Schema$EndpointsApiService;
        /**
         * The entrypoint for the application.
         */
        entrypoint?: Schema$Entrypoint;
        /**
         * App Engine execution environment for this version.Defaults to standard.
         */
        env?: string | null;
        /**
         * Environment variables available to the application.Only returned in GET requests if view=FULL is set.
         */
        envVariables?: {
            [key: string]: string;
        } | null;
        /**
         * Custom static error pages. Limited to 10KB per page.Only returned in GET requests if view=FULL is set.
         */
        errorHandlers?: Schema$ErrorHandler[];
        /**
         * Settings for App Engine flexible runtimes.
         */
        flexibleRuntimeSettings?: Schema$FlexibleRuntimeSettings;
        /**
         * Additional Google Generated Customer Metadata, this field won't be provided by default and can be requested by setting the IncludeExtraData field in GetVersionRequest
         */
        generatedCustomerMetadata?: {
            [key: string]: any;
        } | null;
        /**
         * An ordered list of URL-matching patterns that should be applied to incoming requests. The first matching URL handles the request and other request handlers are not attempted.Only returned in GET requests if view=FULL is set.
         */
        handlers?: Schema$UrlMap[];
        /**
         * Configures health checking for instances. Unhealthy instances are stopped and replaced with new instances. Only applicable in the App Engine flexible environment.
         */
        healthCheck?: Schema$HealthCheck;
        /**
         * Relative name of the version within the service. Example: v1. Version names can contain only lowercase letters, numbers, or hyphens. Reserved names: "default", "latest", and any name with the prefix "ah-".
         */
        id?: string | null;
        /**
         * Before an application can receive email or XMPP messages, the application must be configured to enable the service.
         */
        inboundServices?: string[] | null;
        /**
         * Instance class that is used to run this version. Valid values are: AutomaticScaling: F1, F2, F4, F4_1G ManualScaling or BasicScaling: B1, B2, B4, B8, B4_1GDefaults to F1 for AutomaticScaling and B1 for ManualScaling or BasicScaling.
         */
        instanceClass?: string | null;
        /**
         * Configuration for third-party Python runtime libraries that are required by the application.Only returned in GET requests if view=FULL is set.
         */
        libraries?: Schema$Library[];
        /**
         * Configures liveness health checking for instances. Unhealthy instances are stopped and replaced with new instances
         */
        livenessCheck?: Schema$LivenessCheck;
        /**
         * A service with manual scaling runs continuously, allowing you to perform complex initialization and rely on the state of its memory over time. Manually scaled versions are sometimes referred to as "backends".
         */
        manualScaling?: Schema$ManualScaling;
        /**
         * Output only. Full path to the Version resource in the API. Example: apps/myapp/services/default/versions/v1.@OutputOnly
         */
        name?: string | null;
        /**
         * Extra network settings. Only applicable in the App Engine flexible environment.
         */
        network?: Schema$Network;
        /**
         * Files that match this pattern will not be built into this version. Only applicable for Go runtimes.Only returned in GET requests if view=FULL is set.
         */
        nobuildFilesRegex?: string | null;
        /**
         * Configures readiness health checking for instances. Unhealthy instances are not put into the backend traffic rotation.
         */
        readinessCheck?: Schema$ReadinessCheck;
        /**
         * Machine resources for this version. Only applicable in the App Engine flexible environment.
         */
        resources?: Schema$Resources;
        /**
         * Desired runtime. Example: python27.
         */
        runtime?: string | null;
        /**
         * The version of the API in the given runtime environment. Please see the app.yaml reference for valid values at https://cloud.google.com/appengine/docs/standard//config/appref
         */
        runtimeApiVersion?: string | null;
        /**
         * The channel of the runtime to use. Only available for some runtimes. Defaults to the default channel.
         */
        runtimeChannel?: string | null;
        /**
         * The path or name of the app's main executable.
         */
        runtimeMainExecutablePath?: string | null;
        /**
         * The identity that the deployed version will run as. Admin API will use the App Engine Appspot service account as default if this field is neither provided in app.yaml file nor through CLI flag.
         */
        serviceAccount?: string | null;
        /**
         * Current serving status of this version. Only the versions with a SERVING status create instances and can be billed.SERVING_STATUS_UNSPECIFIED is an invalid value. Defaults to SERVING.
         */
        servingStatus?: string | null;
        /**
         * Whether multiple requests can be dispatched to this version at once.
         */
        threadsafe?: boolean | null;
        /**
         * Output only. Serving URL for this version. Example: "https://myversion-dot-myservice-dot-myapp.appspot.com"@OutputOnly
         */
        versionUrl?: string | null;
        /**
         * Whether to deploy this version in a container on a virtual machine.
         */
        vm?: boolean | null;
        /**
         * Enables VPC connectivity for standard apps.
         */
        vpcAccessConnector?: Schema$VpcAccessConnector;
        /**
         * The Google Compute Engine zones that are supported by this version in the App Engine flexible environment. Deprecated.
         */
        zones?: string[] | null;
    }
    /**
     * Volumes mounted within the app container. Only applicable in the App Engine flexible environment.
     */
    export interface Schema$Volume {
        /**
         * Unique name for the volume.
         */
        name?: string | null;
        /**
         * Volume size in gigabytes.
         */
        sizeGb?: number | null;
        /**
         * Underlying volume type, e.g. 'tmpfs'.
         */
        volumeType?: string | null;
    }
    /**
     * VPC access connector specification.
     */
    export interface Schema$VpcAccessConnector {
        /**
         * The egress setting for the connector, controlling what traffic is diverted through it.
         */
        egressSetting?: string | null;
        /**
         * Full Serverless VPC Access Connector name e.g. projects/my-project/locations/us-central1/connectors/c1.
         */
        name?: string | null;
    }
    /**
     * The zip file information for a zip deployment.
     */
    export interface Schema$ZipInfo {
        /**
         * An estimate of the number of files in a zip for a zip deployment. If set, must be greater than or equal to the actual number of files. Used for optimizing performance; if not provided, deployment may be slow.
         */
        filesCount?: number | null;
        /**
         * URL of the zip file to deploy from. Must be a URL to a resource in Google Cloud Storage in the form 'http(s)://storage.googleapis.com//'.
         */
        sourceUrl?: string | null;
    }
    export class Resource$Apps {
        context: APIRequestContext;
        authorizedCertificates: Resource$Apps$Authorizedcertificates;
        authorizedDomains: Resource$Apps$Authorizeddomains;
        domainMappings: Resource$Apps$Domainmappings;
        firewall: Resource$Apps$Firewall;
        locations: Resource$Apps$Locations;
        operations: Resource$Apps$Operations;
        services: Resource$Apps$Services;
        constructor(context: APIRequestContext);
        /**
         * Creates an App Engine application for a Google Cloud Platform project. Required fields: id - The ID of the target Cloud Platform project. location - The region (https://cloud.google.com/appengine/docs/locations) where you want the App Engine application located.For more information about App Engine applications, see Managing Projects, Applications, and Billing (https://cloud.google.com/appengine/docs/standard/python/console/).
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Apps$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Apps$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Apps$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Apps$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Apps$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets information about an application.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Apps$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Apps$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Application>>;
        get(params: Params$Resource$Apps$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Apps$Get, options: MethodOptions | BodyResponseCallback<Schema$Application>, callback: BodyResponseCallback<Schema$Application>): void;
        get(params: Params$Resource$Apps$Get, callback: BodyResponseCallback<Schema$Application>): void;
        get(callback: BodyResponseCallback<Schema$Application>): void;
        /**
         * Lists all the available runtimes for the application.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        listRuntimes(params: Params$Resource$Apps$Listruntimes, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        listRuntimes(params?: Params$Resource$Apps$Listruntimes, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListRuntimesResponse>>;
        listRuntimes(params: Params$Resource$Apps$Listruntimes, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        listRuntimes(params: Params$Resource$Apps$Listruntimes, options: MethodOptions | BodyResponseCallback<Schema$ListRuntimesResponse>, callback: BodyResponseCallback<Schema$ListRuntimesResponse>): void;
        listRuntimes(params: Params$Resource$Apps$Listruntimes, callback: BodyResponseCallback<Schema$ListRuntimesResponse>): void;
        listRuntimes(callback: BodyResponseCallback<Schema$ListRuntimesResponse>): void;
        /**
         * Updates the specified Application resource. You can update the following fields: auth_domain - Google authentication domain for controlling user access to the application. default_cookie_expiration - Cookie expiration policy for the application. iap - Identity-Aware Proxy properties for the application.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Apps$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Apps$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Apps$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Apps$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Apps$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Recreates the required App Engine features for the specified App Engine application, for example a Cloud Storage bucket or App Engine service account. Use this method if you receive an error message about a missing feature, for example, Error retrieving the App Engine service account. If you have deleted your App Engine service account, this will not be able to recreate it. Instead, you should attempt to use the IAM undelete API if possible at https://cloud.google.com/iam/reference/rest/v1/projects.serviceAccounts/undelete?apix_params=%7B"name"%3A"projects%2F-%2FserviceAccounts%2Funique_id"%2C"resource"%3A%7B%7D%7D . If the deletion was recent, the numeric ID can be found in the Cloud Console Activity Log.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        repair(params: Params$Resource$Apps$Repair, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        repair(params?: Params$Resource$Apps$Repair, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        repair(params: Params$Resource$Apps$Repair, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        repair(params: Params$Resource$Apps$Repair, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        repair(params: Params$Resource$Apps$Repair, callback: BodyResponseCallback<Schema$Operation>): void;
        repair(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Apps$Create extends StandardParameters {
        /**
         * Request body metadata
         */
        requestBody?: Schema$Application;
    }
    export interface Params$Resource$Apps$Get extends StandardParameters {
        /**
         * Part of `name`. Name of the Application resource to get. Example: apps/myapp.
         */
        appsId?: string;
        /**
         * Optional. Options to include extra data
         */
        includeExtraData?: string;
    }
    export interface Params$Resource$Apps$Listruntimes extends StandardParameters {
        /**
         * Part of `parent`. Required. Name of the parent Application resource. Example: apps/myapp.
         */
        appsId?: string;
        /**
         * Optional. The environment of the Application.
         */
        environment?: string;
    }
    export interface Params$Resource$Apps$Patch extends StandardParameters {
        /**
         * Part of `name`. Name of the Application resource to update. Example: apps/myapp.
         */
        appsId?: string;
        /**
         * Required. Standard field mask for the set of fields to be updated.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Application;
    }
    export interface Params$Resource$Apps$Repair extends StandardParameters {
        /**
         * Part of `name`. Name of the application to repair. Example: apps/myapp
         */
        appsId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$RepairApplicationRequest;
    }
    export class Resource$Apps$Authorizedcertificates {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Uploads the specified SSL certificate.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Apps$Authorizedcertificates$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Apps$Authorizedcertificates$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$AuthorizedCertificate>>;
        create(params: Params$Resource$Apps$Authorizedcertificates$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Apps$Authorizedcertificates$Create, options: MethodOptions | BodyResponseCallback<Schema$AuthorizedCertificate>, callback: BodyResponseCallback<Schema$AuthorizedCertificate>): void;
        create(params: Params$Resource$Apps$Authorizedcertificates$Create, callback: BodyResponseCallback<Schema$AuthorizedCertificate>): void;
        create(callback: BodyResponseCallback<Schema$AuthorizedCertificate>): void;
        /**
         * Deletes the specified SSL certificate.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Apps$Authorizedcertificates$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Apps$Authorizedcertificates$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Empty>>;
        delete(params: Params$Resource$Apps$Authorizedcertificates$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Apps$Authorizedcertificates$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Apps$Authorizedcertificates$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets the specified SSL certificate.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Apps$Authorizedcertificates$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Apps$Authorizedcertificates$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$AuthorizedCertificate>>;
        get(params: Params$Resource$Apps$Authorizedcertificates$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Apps$Authorizedcertificates$Get, options: MethodOptions | BodyResponseCallback<Schema$AuthorizedCertificate>, callback: BodyResponseCallback<Schema$AuthorizedCertificate>): void;
        get(params: Params$Resource$Apps$Authorizedcertificates$Get, callback: BodyResponseCallback<Schema$AuthorizedCertificate>): void;
        get(callback: BodyResponseCallback<Schema$AuthorizedCertificate>): void;
        /**
         * Lists all SSL certificates the user is authorized to administer.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Apps$Authorizedcertificates$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Apps$Authorizedcertificates$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListAuthorizedCertificatesResponse>>;
        list(params: Params$Resource$Apps$Authorizedcertificates$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Apps$Authorizedcertificates$List, options: MethodOptions | BodyResponseCallback<Schema$ListAuthorizedCertificatesResponse>, callback: BodyResponseCallback<Schema$ListAuthorizedCertificatesResponse>): void;
        list(params: Params$Resource$Apps$Authorizedcertificates$List, callback: BodyResponseCallback<Schema$ListAuthorizedCertificatesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListAuthorizedCertificatesResponse>): void;
        /**
         * Updates the specified SSL certificate. To renew a certificate and maintain its existing domain mappings, update certificate_data with a new certificate. The new certificate must be applicable to the same domains as the original certificate. The certificate display_name may also be updated.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Apps$Authorizedcertificates$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Apps$Authorizedcertificates$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$AuthorizedCertificate>>;
        patch(params: Params$Resource$Apps$Authorizedcertificates$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Apps$Authorizedcertificates$Patch, options: MethodOptions | BodyResponseCallback<Schema$AuthorizedCertificate>, callback: BodyResponseCallback<Schema$AuthorizedCertificate>): void;
        patch(params: Params$Resource$Apps$Authorizedcertificates$Patch, callback: BodyResponseCallback<Schema$AuthorizedCertificate>): void;
        patch(callback: BodyResponseCallback<Schema$AuthorizedCertificate>): void;
    }
    export interface Params$Resource$Apps$Authorizedcertificates$Create extends StandardParameters {
        /**
         * Part of `parent`. Name of the parent Application resource. Example: apps/myapp.
         */
        appsId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$AuthorizedCertificate;
    }
    export interface Params$Resource$Apps$Authorizedcertificates$Delete extends StandardParameters {
        /**
         * Part of `name`. Name of the resource to delete. Example: apps/myapp/authorizedCertificates/12345.
         */
        appsId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        authorizedCertificatesId?: string;
    }
    export interface Params$Resource$Apps$Authorizedcertificates$Get extends StandardParameters {
        /**
         * Part of `name`. Name of the resource requested. Example: apps/myapp/authorizedCertificates/12345.
         */
        appsId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        authorizedCertificatesId?: string;
        /**
         * Controls the set of fields returned in the GET response.
         */
        view?: string;
    }
    export interface Params$Resource$Apps$Authorizedcertificates$List extends StandardParameters {
        /**
         * Part of `parent`. Name of the parent Application resource. Example: apps/myapp.
         */
        appsId?: string;
        /**
         * Maximum results to return per page.
         */
        pageSize?: number;
        /**
         * Continuation token for fetching the next page of results.
         */
        pageToken?: string;
        /**
         * Controls the set of fields returned in the LIST response.
         */
        view?: string;
    }
    export interface Params$Resource$Apps$Authorizedcertificates$Patch extends StandardParameters {
        /**
         * Part of `name`. Name of the resource to update. Example: apps/myapp/authorizedCertificates/12345.
         */
        appsId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        authorizedCertificatesId?: string;
        /**
         * Standard field mask for the set of fields to be updated. Updates are only supported on the certificate_raw_data and display_name fields.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$AuthorizedCertificate;
    }
    export class Resource$Apps$Authorizeddomains {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Lists all domains the user is authorized to administer.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Apps$Authorizeddomains$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Apps$Authorizeddomains$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListAuthorizedDomainsResponse>>;
        list(params: Params$Resource$Apps$Authorizeddomains$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Apps$Authorizeddomains$List, options: MethodOptions | BodyResponseCallback<Schema$ListAuthorizedDomainsResponse>, callback: BodyResponseCallback<Schema$ListAuthorizedDomainsResponse>): void;
        list(params: Params$Resource$Apps$Authorizeddomains$List, callback: BodyResponseCallback<Schema$ListAuthorizedDomainsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListAuthorizedDomainsResponse>): void;
    }
    export interface Params$Resource$Apps$Authorizeddomains$List extends StandardParameters {
        /**
         * Part of `parent`. Name of the parent Application resource. Example: apps/myapp.
         */
        appsId?: string;
        /**
         * Maximum results to return per page.
         */
        pageSize?: number;
        /**
         * Continuation token for fetching the next page of results.
         */
        pageToken?: string;
    }
    export class Resource$Apps$Domainmappings {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Maps a domain to an application. A user must be authorized to administer a domain in order to map it to an application. For a list of available authorized domains, see AuthorizedDomains.ListAuthorizedDomains.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Apps$Domainmappings$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Apps$Domainmappings$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Apps$Domainmappings$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Apps$Domainmappings$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Apps$Domainmappings$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes the specified domain mapping. A user must be authorized to administer the associated domain in order to delete a DomainMapping resource.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Apps$Domainmappings$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Apps$Domainmappings$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Apps$Domainmappings$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Apps$Domainmappings$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Apps$Domainmappings$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets the specified domain mapping.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Apps$Domainmappings$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Apps$Domainmappings$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$DomainMapping>>;
        get(params: Params$Resource$Apps$Domainmappings$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Apps$Domainmappings$Get, options: MethodOptions | BodyResponseCallback<Schema$DomainMapping>, callback: BodyResponseCallback<Schema$DomainMapping>): void;
        get(params: Params$Resource$Apps$Domainmappings$Get, callback: BodyResponseCallback<Schema$DomainMapping>): void;
        get(callback: BodyResponseCallback<Schema$DomainMapping>): void;
        /**
         * Lists the domain mappings on an application.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Apps$Domainmappings$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Apps$Domainmappings$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListDomainMappingsResponse>>;
        list(params: Params$Resource$Apps$Domainmappings$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Apps$Domainmappings$List, options: MethodOptions | BodyResponseCallback<Schema$ListDomainMappingsResponse>, callback: BodyResponseCallback<Schema$ListDomainMappingsResponse>): void;
        list(params: Params$Resource$Apps$Domainmappings$List, callback: BodyResponseCallback<Schema$ListDomainMappingsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListDomainMappingsResponse>): void;
        /**
         * Updates the specified domain mapping. To map an SSL certificate to a domain mapping, update certificate_id to point to an AuthorizedCertificate resource. A user must be authorized to administer the associated domain in order to update a DomainMapping resource.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Apps$Domainmappings$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Apps$Domainmappings$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Apps$Domainmappings$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Apps$Domainmappings$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Apps$Domainmappings$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Apps$Domainmappings$Create extends StandardParameters {
        /**
         * Part of `parent`. Name of the parent Application resource. Example: apps/myapp.
         */
        appsId?: string;
        /**
         * Whether the domain creation should override any existing mappings for this domain. By default, overrides are rejected.
         */
        overrideStrategy?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$DomainMapping;
    }
    export interface Params$Resource$Apps$Domainmappings$Delete extends StandardParameters {
        /**
         * Part of `name`. Name of the resource to delete. Example: apps/myapp/domainMappings/example.com.
         */
        appsId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        domainMappingsId?: string;
    }
    export interface Params$Resource$Apps$Domainmappings$Get extends StandardParameters {
        /**
         * Part of `name`. Name of the resource requested. Example: apps/myapp/domainMappings/example.com.
         */
        appsId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        domainMappingsId?: string;
    }
    export interface Params$Resource$Apps$Domainmappings$List extends StandardParameters {
        /**
         * Part of `parent`. Name of the parent Application resource. Example: apps/myapp.
         */
        appsId?: string;
        /**
         * Maximum results to return per page.
         */
        pageSize?: number;
        /**
         * Continuation token for fetching the next page of results.
         */
        pageToken?: string;
    }
    export interface Params$Resource$Apps$Domainmappings$Patch extends StandardParameters {
        /**
         * Part of `name`. Name of the resource to update. Example: apps/myapp/domainMappings/example.com.
         */
        appsId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        domainMappingsId?: string;
        /**
         * Required. Standard field mask for the set of fields to be updated.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$DomainMapping;
    }
    export class Resource$Apps$Firewall {
        context: APIRequestContext;
        ingressRules: Resource$Apps$Firewall$Ingressrules;
        constructor(context: APIRequestContext);
    }
    export class Resource$Apps$Firewall$Ingressrules {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Replaces the entire firewall ruleset in one bulk operation. This overrides and replaces the rules of an existing firewall with the new rules.If the final rule does not match traffic with the '*' wildcard IP range, then an "allow all" rule is explicitly added to the end of the list.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        batchUpdate(params: Params$Resource$Apps$Firewall$Ingressrules$Batchupdate, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        batchUpdate(params?: Params$Resource$Apps$Firewall$Ingressrules$Batchupdate, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$BatchUpdateIngressRulesResponse>>;
        batchUpdate(params: Params$Resource$Apps$Firewall$Ingressrules$Batchupdate, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        batchUpdate(params: Params$Resource$Apps$Firewall$Ingressrules$Batchupdate, options: MethodOptions | BodyResponseCallback<Schema$BatchUpdateIngressRulesResponse>, callback: BodyResponseCallback<Schema$BatchUpdateIngressRulesResponse>): void;
        batchUpdate(params: Params$Resource$Apps$Firewall$Ingressrules$Batchupdate, callback: BodyResponseCallback<Schema$BatchUpdateIngressRulesResponse>): void;
        batchUpdate(callback: BodyResponseCallback<Schema$BatchUpdateIngressRulesResponse>): void;
        /**
         * Creates a firewall rule for the application.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Apps$Firewall$Ingressrules$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Apps$Firewall$Ingressrules$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$FirewallRule>>;
        create(params: Params$Resource$Apps$Firewall$Ingressrules$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Apps$Firewall$Ingressrules$Create, options: MethodOptions | BodyResponseCallback<Schema$FirewallRule>, callback: BodyResponseCallback<Schema$FirewallRule>): void;
        create(params: Params$Resource$Apps$Firewall$Ingressrules$Create, callback: BodyResponseCallback<Schema$FirewallRule>): void;
        create(callback: BodyResponseCallback<Schema$FirewallRule>): void;
        /**
         * Deletes the specified firewall rule.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Apps$Firewall$Ingressrules$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Apps$Firewall$Ingressrules$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Empty>>;
        delete(params: Params$Resource$Apps$Firewall$Ingressrules$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Apps$Firewall$Ingressrules$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Apps$Firewall$Ingressrules$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets the specified firewall rule.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Apps$Firewall$Ingressrules$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Apps$Firewall$Ingressrules$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$FirewallRule>>;
        get(params: Params$Resource$Apps$Firewall$Ingressrules$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Apps$Firewall$Ingressrules$Get, options: MethodOptions | BodyResponseCallback<Schema$FirewallRule>, callback: BodyResponseCallback<Schema$FirewallRule>): void;
        get(params: Params$Resource$Apps$Firewall$Ingressrules$Get, callback: BodyResponseCallback<Schema$FirewallRule>): void;
        get(callback: BodyResponseCallback<Schema$FirewallRule>): void;
        /**
         * Lists the firewall rules of an application.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Apps$Firewall$Ingressrules$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Apps$Firewall$Ingressrules$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListIngressRulesResponse>>;
        list(params: Params$Resource$Apps$Firewall$Ingressrules$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Apps$Firewall$Ingressrules$List, options: MethodOptions | BodyResponseCallback<Schema$ListIngressRulesResponse>, callback: BodyResponseCallback<Schema$ListIngressRulesResponse>): void;
        list(params: Params$Resource$Apps$Firewall$Ingressrules$List, callback: BodyResponseCallback<Schema$ListIngressRulesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListIngressRulesResponse>): void;
        /**
         * Updates the specified firewall rule.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Apps$Firewall$Ingressrules$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Apps$Firewall$Ingressrules$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$FirewallRule>>;
        patch(params: Params$Resource$Apps$Firewall$Ingressrules$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Apps$Firewall$Ingressrules$Patch, options: MethodOptions | BodyResponseCallback<Schema$FirewallRule>, callback: BodyResponseCallback<Schema$FirewallRule>): void;
        patch(params: Params$Resource$Apps$Firewall$Ingressrules$Patch, callback: BodyResponseCallback<Schema$FirewallRule>): void;
        patch(callback: BodyResponseCallback<Schema$FirewallRule>): void;
    }
    export interface Params$Resource$Apps$Firewall$Ingressrules$Batchupdate extends StandardParameters {
        /**
         * Part of `name`. Name of the Firewall collection to set. Example: apps/myapp/firewall/ingressRules.
         */
        appsId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$BatchUpdateIngressRulesRequest;
    }
    export interface Params$Resource$Apps$Firewall$Ingressrules$Create extends StandardParameters {
        /**
         * Part of `parent`. Name of the parent Firewall collection in which to create a new rule. Example: apps/myapp/firewall/ingressRules.
         */
        appsId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$FirewallRule;
    }
    export interface Params$Resource$Apps$Firewall$Ingressrules$Delete extends StandardParameters {
        /**
         * Part of `name`. Name of the Firewall resource to delete. Example: apps/myapp/firewall/ingressRules/100.
         */
        appsId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        ingressRulesId?: string;
    }
    export interface Params$Resource$Apps$Firewall$Ingressrules$Get extends StandardParameters {
        /**
         * Part of `name`. Name of the Firewall resource to retrieve. Example: apps/myapp/firewall/ingressRules/100.
         */
        appsId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        ingressRulesId?: string;
    }
    export interface Params$Resource$Apps$Firewall$Ingressrules$List extends StandardParameters {
        /**
         * Part of `parent`. Name of the Firewall collection to retrieve. Example: apps/myapp/firewall/ingressRules.
         */
        appsId?: string;
        /**
         * A valid IP Address. If set, only rules matching this address will be returned. The first returned rule will be the rule that fires on requests from this IP.
         */
        matchingAddress?: string;
        /**
         * Maximum results to return per page.
         */
        pageSize?: number;
        /**
         * Continuation token for fetching the next page of results.
         */
        pageToken?: string;
    }
    export interface Params$Resource$Apps$Firewall$Ingressrules$Patch extends StandardParameters {
        /**
         * Part of `name`. Name of the Firewall resource to update. Example: apps/myapp/firewall/ingressRules/100.
         */
        appsId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        ingressRulesId?: string;
        /**
         * Standard field mask for the set of fields to be updated.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$FirewallRule;
    }
    export class Resource$Apps$Locations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets information about a location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Apps$Locations$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Apps$Locations$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Location>>;
        get(params: Params$Resource$Apps$Locations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Apps$Locations$Get, options: MethodOptions | BodyResponseCallback<Schema$Location>, callback: BodyResponseCallback<Schema$Location>): void;
        get(params: Params$Resource$Apps$Locations$Get, callback: BodyResponseCallback<Schema$Location>): void;
        get(callback: BodyResponseCallback<Schema$Location>): void;
        /**
         * Lists information about the supported locations for this service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Apps$Locations$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Apps$Locations$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListLocationsResponse>>;
        list(params: Params$Resource$Apps$Locations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Apps$Locations$List, options: MethodOptions | BodyResponseCallback<Schema$ListLocationsResponse>, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(params: Params$Resource$Apps$Locations$List, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
    }
    export interface Params$Resource$Apps$Locations$Get extends StandardParameters {
        /**
         * Part of `name`. Resource name for the location.
         */
        appsId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        locationsId?: string;
    }
    export interface Params$Resource$Apps$Locations$List extends StandardParameters {
        /**
         * Part of `name`. The resource that owns the locations collection, if applicable.
         */
        appsId?: string;
        /**
         * Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.
         */
        extraLocationTypes?: string[];
        /**
         * A filter to narrow down results to a preferred subset. The filtering language accepts strings like "displayName=tokyo", and is documented in more detail in AIP-160 (https://google.aip.dev/160).
         */
        filter?: string;
        /**
         * The maximum number of results to return. If not set, the service selects a default.
         */
        pageSize?: number;
        /**
         * A page token received from the next_page_token field in the response. Send that page token to receive the subsequent page.
         */
        pageToken?: string;
    }
    export class Resource$Apps$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Apps$Operations$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Apps$Operations$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        get(params: Params$Resource$Apps$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Apps$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        get(params: Params$Resource$Apps$Operations$Get, callback: BodyResponseCallback<Schema$Operation>): void;
        get(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns UNIMPLEMENTED.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Apps$Operations$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Apps$Operations$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListOperationsResponse>>;
        list(params: Params$Resource$Apps$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Apps$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$ListOperationsResponse>, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(params: Params$Resource$Apps$Operations$List, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
    }
    export interface Params$Resource$Apps$Operations$Get extends StandardParameters {
        /**
         * Part of `name`. The name of the operation resource.
         */
        appsId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        operationsId?: string;
    }
    export interface Params$Resource$Apps$Operations$List extends StandardParameters {
        /**
         * Part of `name`. The name of the operation's parent resource.
         */
        appsId?: string;
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
    }
    export class Resource$Apps$Services {
        context: APIRequestContext;
        versions: Resource$Apps$Services$Versions;
        constructor(context: APIRequestContext);
        /**
         * Deletes the specified service and all enclosed versions.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Apps$Services$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Apps$Services$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Apps$Services$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Apps$Services$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Apps$Services$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets the current configuration of the specified service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Apps$Services$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Apps$Services$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Service>>;
        get(params: Params$Resource$Apps$Services$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Apps$Services$Get, options: MethodOptions | BodyResponseCallback<Schema$Service>, callback: BodyResponseCallback<Schema$Service>): void;
        get(params: Params$Resource$Apps$Services$Get, callback: BodyResponseCallback<Schema$Service>): void;
        get(callback: BodyResponseCallback<Schema$Service>): void;
        /**
         * Lists all the services in the application.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Apps$Services$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Apps$Services$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListServicesResponse>>;
        list(params: Params$Resource$Apps$Services$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Apps$Services$List, options: MethodOptions | BodyResponseCallback<Schema$ListServicesResponse>, callback: BodyResponseCallback<Schema$ListServicesResponse>): void;
        list(params: Params$Resource$Apps$Services$List, callback: BodyResponseCallback<Schema$ListServicesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListServicesResponse>): void;
        /**
         * Updates the configuration of the specified service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Apps$Services$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Apps$Services$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Apps$Services$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Apps$Services$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Apps$Services$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Apps$Services$Delete extends StandardParameters {
        /**
         * Part of `name`. Name of the resource requested. Example: apps/myapp/services/default.
         */
        appsId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        servicesId?: string;
    }
    export interface Params$Resource$Apps$Services$Get extends StandardParameters {
        /**
         * Part of `name`. Name of the resource requested. Example: apps/myapp/services/default.
         */
        appsId?: string;
        /**
         * Optional. Options to include extra data
         */
        includeExtraData?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        servicesId?: string;
    }
    export interface Params$Resource$Apps$Services$List extends StandardParameters {
        /**
         * Part of `parent`. Name of the parent Application resource. Example: apps/myapp.
         */
        appsId?: string;
        /**
         * Maximum results to return per page.
         */
        pageSize?: number;
        /**
         * Continuation token for fetching the next page of results.
         */
        pageToken?: string;
    }
    export interface Params$Resource$Apps$Services$Patch extends StandardParameters {
        /**
         * Part of `name`. Name of the resource to update. Example: apps/myapp/services/default.
         */
        appsId?: string;
        /**
         * Set to true to gradually shift traffic to one or more versions that you specify. By default, traffic is shifted immediately. For gradual traffic migration, the target versions must be located within instances that are configured for both warmup requests (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#InboundServiceType) and automatic scaling (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#AutomaticScaling). You must specify the shardBy (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services#ShardBy) field in the Service resource. Gradual traffic migration is not supported in the App Engine flexible environment. For examples, see Migrating and Splitting Traffic (https://cloud.google.com/appengine/docs/admin-api/migrating-splitting-traffic).
         */
        migrateTraffic?: boolean;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        servicesId?: string;
        /**
         * Required. Standard field mask for the set of fields to be updated.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Service;
    }
    export class Resource$Apps$Services$Versions {
        context: APIRequestContext;
        instances: Resource$Apps$Services$Versions$Instances;
        constructor(context: APIRequestContext);
        /**
         * Deploys code and resource files to a new version.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Apps$Services$Versions$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Apps$Services$Versions$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Apps$Services$Versions$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Apps$Services$Versions$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Apps$Services$Versions$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes an existing Version resource.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Apps$Services$Versions$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Apps$Services$Versions$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Apps$Services$Versions$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Apps$Services$Versions$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Apps$Services$Versions$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets the specified Version resource. By default, only a BASIC_VIEW will be returned. Specify the FULL_VIEW parameter to get the full resource.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Apps$Services$Versions$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Apps$Services$Versions$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Version>>;
        get(params: Params$Resource$Apps$Services$Versions$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Apps$Services$Versions$Get, options: MethodOptions | BodyResponseCallback<Schema$Version>, callback: BodyResponseCallback<Schema$Version>): void;
        get(params: Params$Resource$Apps$Services$Versions$Get, callback: BodyResponseCallback<Schema$Version>): void;
        get(callback: BodyResponseCallback<Schema$Version>): void;
        /**
         * Lists the versions of a service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Apps$Services$Versions$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Apps$Services$Versions$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListVersionsResponse>>;
        list(params: Params$Resource$Apps$Services$Versions$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Apps$Services$Versions$List, options: MethodOptions | BodyResponseCallback<Schema$ListVersionsResponse>, callback: BodyResponseCallback<Schema$ListVersionsResponse>): void;
        list(params: Params$Resource$Apps$Services$Versions$List, callback: BodyResponseCallback<Schema$ListVersionsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListVersionsResponse>): void;
        /**
         * Updates the specified Version resource. You can specify the following fields depending on the App Engine environment and type of scaling that the version resource uses:Standard environment instance_class (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#Version.FIELDS.instance_class)automatic scaling in the standard environment: automatic_scaling.min_idle_instances (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#Version.FIELDS.automatic_scaling) automatic_scaling.max_idle_instances (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#Version.FIELDS.automatic_scaling) automaticScaling.standard_scheduler_settings.max_instances (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#StandardSchedulerSettings) automaticScaling.standard_scheduler_settings.min_instances (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#StandardSchedulerSettings) automaticScaling.standard_scheduler_settings.target_cpu_utilization (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#StandardSchedulerSettings) automaticScaling.standard_scheduler_settings.target_throughput_utilization (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#StandardSchedulerSettings)basic scaling or manual scaling in the standard environment: serving_status (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#Version.FIELDS.serving_status) manual_scaling.instances (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#manualscaling)Flexible environment serving_status (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#Version.FIELDS.serving_status)automatic scaling in the flexible environment: automatic_scaling.min_total_instances (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#Version.FIELDS.automatic_scaling) automatic_scaling.max_total_instances (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#Version.FIELDS.automatic_scaling) automatic_scaling.cool_down_period_sec (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#Version.FIELDS.automatic_scaling) automatic_scaling.cpu_utilization.target_utilization (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#Version.FIELDS.automatic_scaling)manual scaling in the flexible environment: manual_scaling.instances (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#manualscaling)
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Apps$Services$Versions$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Apps$Services$Versions$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Apps$Services$Versions$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Apps$Services$Versions$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Apps$Services$Versions$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Apps$Services$Versions$Create extends StandardParameters {
        /**
         * Part of `parent`. Name of the parent resource to create this version under. Example: apps/myapp/services/default.
         */
        appsId?: string;
        /**
         * Part of `parent`. See documentation of `appsId`.
         */
        servicesId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Version;
    }
    export interface Params$Resource$Apps$Services$Versions$Delete extends StandardParameters {
        /**
         * Part of `name`. Name of the resource requested. Example: apps/myapp/services/default/versions/v1.
         */
        appsId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        servicesId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        versionsId?: string;
    }
    export interface Params$Resource$Apps$Services$Versions$Get extends StandardParameters {
        /**
         * Part of `name`. Name of the resource requested. Example: apps/myapp/services/default/versions/v1.
         */
        appsId?: string;
        /**
         * Optional. Options to include extra data
         */
        includeExtraData?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        servicesId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        versionsId?: string;
        /**
         * Controls the set of fields returned in the Get response.
         */
        view?: string;
    }
    export interface Params$Resource$Apps$Services$Versions$List extends StandardParameters {
        /**
         * Part of `parent`. Name of the parent Service resource. Example: apps/myapp/services/default.
         */
        appsId?: string;
        /**
         * Maximum results to return per page.
         */
        pageSize?: number;
        /**
         * Continuation token for fetching the next page of results.
         */
        pageToken?: string;
        /**
         * Part of `parent`. See documentation of `appsId`.
         */
        servicesId?: string;
        /**
         * Controls the set of fields returned in the List response.
         */
        view?: string;
    }
    export interface Params$Resource$Apps$Services$Versions$Patch extends StandardParameters {
        /**
         * Part of `name`. Name of the resource to update. Example: apps/myapp/services/default/versions/1.
         */
        appsId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        servicesId?: string;
        /**
         * Standard field mask for the set of fields to be updated.
         */
        updateMask?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        versionsId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Version;
    }
    export class Resource$Apps$Services$Versions$Instances {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Enables debugging on a VM instance. This allows you to use the SSH command to connect to the virtual machine where the instance lives. While in "debug mode", the instance continues to serve live traffic. You should delete the instance when you are done debugging and then allow the system to take over and determine if another instance should be started.Only applicable for instances in App Engine flexible environment.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        debug(params: Params$Resource$Apps$Services$Versions$Instances$Debug, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        debug(params?: Params$Resource$Apps$Services$Versions$Instances$Debug, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        debug(params: Params$Resource$Apps$Services$Versions$Instances$Debug, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        debug(params: Params$Resource$Apps$Services$Versions$Instances$Debug, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        debug(params: Params$Resource$Apps$Services$Versions$Instances$Debug, callback: BodyResponseCallback<Schema$Operation>): void;
        debug(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Stops a running instance.The instance might be automatically recreated based on the scaling settings of the version. For more information, see "How Instances are Managed" (standard environment (https://cloud.google.com/appengine/docs/standard/python/how-instances-are-managed) | flexible environment (https://cloud.google.com/appengine/docs/flexible/python/how-instances-are-managed)).To ensure that instances are not re-created and avoid getting billed, you can stop all instances within the target version by changing the serving status of the version to STOPPED with the apps.services.versions.patch (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1/apps.services.versions/patch) method.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Apps$Services$Versions$Instances$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Apps$Services$Versions$Instances$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Apps$Services$Versions$Instances$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Apps$Services$Versions$Instances$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Apps$Services$Versions$Instances$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets instance information.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Apps$Services$Versions$Instances$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Apps$Services$Versions$Instances$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Instance>>;
        get(params: Params$Resource$Apps$Services$Versions$Instances$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Apps$Services$Versions$Instances$Get, options: MethodOptions | BodyResponseCallback<Schema$Instance>, callback: BodyResponseCallback<Schema$Instance>): void;
        get(params: Params$Resource$Apps$Services$Versions$Instances$Get, callback: BodyResponseCallback<Schema$Instance>): void;
        get(callback: BodyResponseCallback<Schema$Instance>): void;
        /**
         * Lists the instances of a version.Tip: To aggregate details about instances over time, see the Stackdriver Monitoring API (https://cloud.google.com/monitoring/api/ref_v3/rest/v3/projects.timeSeries/list).
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Apps$Services$Versions$Instances$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Apps$Services$Versions$Instances$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListInstancesResponse>>;
        list(params: Params$Resource$Apps$Services$Versions$Instances$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Apps$Services$Versions$Instances$List, options: MethodOptions | BodyResponseCallback<Schema$ListInstancesResponse>, callback: BodyResponseCallback<Schema$ListInstancesResponse>): void;
        list(params: Params$Resource$Apps$Services$Versions$Instances$List, callback: BodyResponseCallback<Schema$ListInstancesResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListInstancesResponse>): void;
    }
    export interface Params$Resource$Apps$Services$Versions$Instances$Debug extends StandardParameters {
        /**
         * Part of `name`. Name of the resource requested. Example: apps/myapp/services/default/versions/v1/instances/instance-1.
         */
        appsId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        instancesId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        servicesId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        versionsId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$DebugInstanceRequest;
    }
    export interface Params$Resource$Apps$Services$Versions$Instances$Delete extends StandardParameters {
        /**
         * Part of `name`. Name of the resource requested. Example: apps/myapp/services/default/versions/v1/instances/instance-1.
         */
        appsId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        instancesId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        servicesId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        versionsId?: string;
    }
    export interface Params$Resource$Apps$Services$Versions$Instances$Get extends StandardParameters {
        /**
         * Part of `name`. Name of the resource requested. Example: apps/myapp/services/default/versions/v1/instances/instance-1.
         */
        appsId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        instancesId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        servicesId?: string;
        /**
         * Part of `name`. See documentation of `appsId`.
         */
        versionsId?: string;
    }
    export interface Params$Resource$Apps$Services$Versions$Instances$List extends StandardParameters {
        /**
         * Part of `parent`. Name of the parent Version resource. Example: apps/myapp/services/default/versions/v1.
         */
        appsId?: string;
        /**
         * Maximum results to return per page.
         */
        pageSize?: number;
        /**
         * Continuation token for fetching the next page of results.
         */
        pageToken?: string;
        /**
         * Part of `parent`. See documentation of `appsId`.
         */
        servicesId?: string;
        /**
         * Part of `parent`. See documentation of `appsId`.
         */
        versionsId?: string;
    }
    export class Resource$Projects {
        context: APIRequestContext;
        locations: Resource$Projects$Locations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations {
        context: APIRequestContext;
        applications: Resource$Projects$Locations$Applications;
        operations: Resource$Projects$Locations$Operations;
        constructor(context: APIRequestContext);
        /**
         * Gets information about a location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Location>>;
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Get, options: MethodOptions | BodyResponseCallback<Schema$Location>, callback: BodyResponseCallback<Schema$Location>): void;
        get(params: Params$Resource$Projects$Locations$Get, callback: BodyResponseCallback<Schema$Location>): void;
        get(callback: BodyResponseCallback<Schema$Location>): void;
        /**
         * Lists information about the supported locations for this service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListLocationsResponse>>;
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$List, options: MethodOptions | BodyResponseCallback<Schema$ListLocationsResponse>, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$List, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Get extends StandardParameters {
        /**
         * Part of `name`. See documentation of `projectsId`.
         */
        locationsId?: string;
        /**
         * Part of `name`. Resource name for the location.
         */
        projectsId?: string;
    }
    export interface Params$Resource$Projects$Locations$List extends StandardParameters {
        /**
         * Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.
         */
        extraLocationTypes?: string[];
        /**
         * A filter to narrow down results to a preferred subset. The filtering language accepts strings like "displayName=tokyo", and is documented in more detail in AIP-160 (https://google.aip.dev/160).
         */
        filter?: string;
        /**
         * The maximum number of results to return. If not set, the service selects a default.
         */
        pageSize?: number;
        /**
         * A page token received from the next_page_token field in the response. Send that page token to receive the subsequent page.
         */
        pageToken?: string;
        /**
         * Part of `name`. The resource that owns the locations collection, if applicable.
         */
        projectsId?: string;
    }
    export class Resource$Projects$Locations$Applications {
        context: APIRequestContext;
        authorizedDomains: Resource$Projects$Locations$Applications$Authorizeddomains;
        services: Resource$Projects$Locations$Applications$Services;
        constructor(context: APIRequestContext);
        /**
         * Updates the specified Application resource. You can update the following fields: auth_domain - Google authentication domain for controlling user access to the application. default_cookie_expiration - Cookie expiration policy for the application. iap - Identity-Aware Proxy properties for the application.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Applications$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Applications$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Applications$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Applications$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Applications$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Applications$Patch extends StandardParameters {
        /**
         * Part of `name`. See documentation of `projectsId`.
         */
        applicationsId?: string;
        /**
         * Part of `name`. See documentation of `projectsId`.
         */
        locationsId?: string;
        /**
         * Part of `name`. Name of the Application resource to update. Example: apps/myapp.
         */
        projectsId?: string;
        /**
         * Required. Standard field mask for the set of fields to be updated.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Application;
    }
    export class Resource$Projects$Locations$Applications$Authorizeddomains {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Lists all domains the user is authorized to administer.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Applications$Authorizeddomains$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Applications$Authorizeddomains$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListAuthorizedDomainsResponse>>;
        list(params: Params$Resource$Projects$Locations$Applications$Authorizeddomains$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Applications$Authorizeddomains$List, options: MethodOptions | BodyResponseCallback<Schema$ListAuthorizedDomainsResponse>, callback: BodyResponseCallback<Schema$ListAuthorizedDomainsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Applications$Authorizeddomains$List, callback: BodyResponseCallback<Schema$ListAuthorizedDomainsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListAuthorizedDomainsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Applications$Authorizeddomains$List extends StandardParameters {
        /**
         * Part of `parent`. See documentation of `projectsId`.
         */
        applicationsId?: string;
        /**
         * Part of `parent`. See documentation of `projectsId`.
         */
        locationsId?: string;
        /**
         * Maximum results to return per page.
         */
        pageSize?: number;
        /**
         * Continuation token for fetching the next page of results.
         */
        pageToken?: string;
        /**
         * Part of `parent`. Name of the parent Application resource. Example: apps/myapp.
         */
        projectsId?: string;
    }
    export class Resource$Projects$Locations$Applications$Services {
        context: APIRequestContext;
        versions: Resource$Projects$Locations$Applications$Services$Versions;
        constructor(context: APIRequestContext);
        /**
         * Deletes the specified service and all enclosed versions.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Applications$Services$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Applications$Services$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Applications$Services$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Applications$Services$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Applications$Services$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Applications$Services$Delete extends StandardParameters {
        /**
         * Part of `name`. See documentation of `projectsId`.
         */
        applicationsId?: string;
        /**
         * Part of `name`. See documentation of `projectsId`.
         */
        locationsId?: string;
        /**
         * Part of `name`. Name of the resource requested. Example: apps/myapp/services/default.
         */
        projectsId?: string;
        /**
         * Part of `name`. See documentation of `projectsId`.
         */
        servicesId?: string;
    }
    export class Resource$Projects$Locations$Applications$Services$Versions {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Deletes an existing Version resource.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Applications$Services$Versions$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Applications$Services$Versions$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Applications$Services$Versions$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Applications$Services$Versions$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Applications$Services$Versions$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Updates the specified Version resource. You can specify the following fields depending on the App Engine environment and type of scaling that the version resource uses:Standard environment instance_class (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#Version.FIELDS.instance_class)automatic scaling in the standard environment: automatic_scaling.min_idle_instances (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#Version.FIELDS.automatic_scaling) automatic_scaling.max_idle_instances (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#Version.FIELDS.automatic_scaling) automaticScaling.standard_scheduler_settings.max_instances (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#StandardSchedulerSettings) automaticScaling.standard_scheduler_settings.min_instances (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#StandardSchedulerSettings) automaticScaling.standard_scheduler_settings.target_cpu_utilization (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#StandardSchedulerSettings) automaticScaling.standard_scheduler_settings.target_throughput_utilization (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#StandardSchedulerSettings)basic scaling or manual scaling in the standard environment: serving_status (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#Version.FIELDS.serving_status) manual_scaling.instances (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#manualscaling)Flexible environment serving_status (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#Version.FIELDS.serving_status)automatic scaling in the flexible environment: automatic_scaling.min_total_instances (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#Version.FIELDS.automatic_scaling) automatic_scaling.max_total_instances (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#Version.FIELDS.automatic_scaling) automatic_scaling.cool_down_period_sec (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#Version.FIELDS.automatic_scaling) automatic_scaling.cpu_utilization.target_utilization (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#Version.FIELDS.automatic_scaling)manual scaling in the flexible environment: manual_scaling.instances (https://cloud.google.com/appengine/docs/admin-api/reference/rest/v1beta/apps.services.versions#manualscaling)
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Applications$Services$Versions$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Applications$Services$Versions$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Applications$Services$Versions$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Applications$Services$Versions$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Applications$Services$Versions$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Applications$Services$Versions$Delete extends StandardParameters {
        /**
         * Part of `name`. See documentation of `projectsId`.
         */
        applicationsId?: string;
        /**
         * Part of `name`. See documentation of `projectsId`.
         */
        locationsId?: string;
        /**
         * Part of `name`. Name of the resource requested. Example: apps/myapp/services/default/versions/v1.
         */
        projectsId?: string;
        /**
         * Part of `name`. See documentation of `projectsId`.
         */
        servicesId?: string;
        /**
         * Part of `name`. See documentation of `projectsId`.
         */
        versionsId?: string;
    }
    export interface Params$Resource$Projects$Locations$Applications$Services$Versions$Patch extends StandardParameters {
        /**
         * Part of `name`. See documentation of `projectsId`.
         */
        applicationsId?: string;
        /**
         * Part of `name`. See documentation of `projectsId`.
         */
        locationsId?: string;
        /**
         * Part of `name`. Name of the resource to update. Example: apps/myapp/services/default/versions/1.
         */
        projectsId?: string;
        /**
         * Part of `name`. See documentation of `projectsId`.
         */
        servicesId?: string;
        /**
         * Standard field mask for the set of fields to be updated.
         */
        updateMask?: string;
        /**
         * Part of `name`. See documentation of `projectsId`.
         */
        versionsId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Version;
    }
    export class Resource$Projects$Locations$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Operations$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, callback: BodyResponseCallback<Schema$Operation>): void;
        get(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns UNIMPLEMENTED.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Operations$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListOperationsResponse>>;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$ListOperationsResponse>, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Operations$Get extends StandardParameters {
        /**
         * Part of `name`. See documentation of `projectsId`.
         */
        locationsId?: string;
        /**
         * Part of `name`. See documentation of `projectsId`.
         */
        operationsId?: string;
        /**
         * Part of `name`. The name of the operation resource.
         */
        projectsId?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$List extends StandardParameters {
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * Part of `name`. See documentation of `projectsId`.
         */
        locationsId?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
        /**
         * Part of `name`. The name of the operation's parent resource.
         */
        projectsId?: string;
    }
    export {};
}
