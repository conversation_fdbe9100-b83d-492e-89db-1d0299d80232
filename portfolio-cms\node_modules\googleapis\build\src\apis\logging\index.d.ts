/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { logging_v2 } from './v2';
export declare const VERSIONS: {
    v2: typeof logging_v2.Logging;
};
export declare function logging(version: 'v2'): logging_v2.Logging;
export declare function logging(options: logging_v2.Options): logging_v2.Logging;
declare const auth: AuthPlus;
export { auth };
export { logging_v2 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
