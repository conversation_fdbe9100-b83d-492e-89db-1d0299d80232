/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { integrations_v1alpha } from './v1alpha';
export declare const VERSIONS: {
    v1alpha: typeof integrations_v1alpha.Integrations;
};
export declare function integrations(version: 'v1alpha'): integrations_v1alpha.Integrations;
export declare function integrations(options: integrations_v1alpha.Options): integrations_v1alpha.Integrations;
declare const auth: AuthPlus;
export { auth };
export { integrations_v1alpha };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
